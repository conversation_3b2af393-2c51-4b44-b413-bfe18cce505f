[gd_scene load_steps=4 format=3 uid="uid://hqrw4ih0y1fp"]

[ext_resource type="Script" uid="uid://vggnf13y6ygr" path="res://scripts/UI/MainMenu.cs" id="1_i2xx2"]
[ext_resource type="Texture2D" uid="uid://bvskneg5gio3" path="res://Assets/Levels/MapTemp.png" id="2_i2xx2"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_i2xx2"]
texture = ExtResource("2_i2xx2")

[node name="MainMenu" type="Control"]
z_index = 10
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(1004, 518)
script = ExtResource("1_i2xx2")

[node name="Panel" type="Panel" parent="."]
custom_minimum_size = Vector2(1920, 1080)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_i2xx2")
metadata/_edit_use_anchors_ = true

[node name="VBoxContainer" type="VBoxContainer" parent="."]
custom_minimum_size = Vector2(100, 100)
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 100.0
offset_top = -355.0
offset_right = 383.0
offset_bottom = -185.0
grow_vertical = 0
scale = Vector2(1.5, 1.5)
alignment = 1

[node name="StartButton" type="Button" parent="VBoxContainer" groups=["Menu buttons"]]
layout_mode = 2
focus_neighbor_top = NodePath("../QuitButton")
text = "Play
"

[node name="MarginContainer" type="MarginContainer" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2

[node name="OptionButton" type="Button" parent="VBoxContainer" groups=["Menu buttons"]]
layout_mode = 2
size_flags_vertical = 4
text = "Options"

[node name="MarginContainer2" type="MarginContainer" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2

[node name="QuitButton" type="Button" parent="VBoxContainer" groups=["Menu buttons"]]
layout_mode = 2
size_flags_vertical = 4
focus_neighbor_bottom = NodePath("../StartButton")
text = "Quit"

[node name="VSplitContainer" type="VSplitContainer" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -667.0
offset_top = -130.0
offset_right = -260.0
offset_bottom = -71.9999
grow_horizontal = 0
grow_vertical = 0
scale = Vector2(1.5, 1.5)

[node name="ControlLayout" type="Label" parent="VSplitContainer"]
layout_mode = 2
text = "Controls:"

[node name="HBoxContainer" type="HBoxContainer" parent="VSplitContainer"]
layout_mode = 2
size_flags_horizontal = 10
size_flags_vertical = 10

[node name="Label" type="Label" parent="VSplitContainer/HBoxContainer"]
layout_mode = 2
text = "Stop: Spacebar"

[node name="MarginContainer" type="MarginContainer" parent="VSplitContainer/HBoxContainer"]
custom_minimum_size = Vector2(10, 0)
layout_mode = 2

[node name="Label2" type="Label" parent="VSplitContainer/HBoxContainer"]
layout_mode = 2
text = "Right: D"

[node name="MarginContainer2" type="MarginContainer" parent="VSplitContainer/HBoxContainer"]
custom_minimum_size = Vector2(10, 0)
layout_mode = 2

[node name="Label3" type="Label" parent="VSplitContainer/HBoxContainer"]
layout_mode = 2
text = "Left: A"

[node name="MarginContainer3" type="MarginContainer" parent="VSplitContainer/HBoxContainer"]
custom_minimum_size = Vector2(10, 0)
layout_mode = 2

[node name="Label4" type="Label" parent="VSplitContainer/HBoxContainer"]
layout_mode = 2
text = "Up: W"

[node name="MarginContainer4" type="MarginContainer" parent="VSplitContainer/HBoxContainer"]
custom_minimum_size = Vector2(10, 0)
layout_mode = 2

[node name="Label5" type="Label" parent="VSplitContainer/HBoxContainer"]
layout_mode = 2
text = "Down: S"

[connection signal="mouse_entered" from="VBoxContainer/StartButton" to="." method="OnHover"]
[connection signal="mouse_entered" from="VBoxContainer/OptionButton" to="." method="OnHover"]
[connection signal="mouse_entered" from="VBoxContainer/QuitButton" to="." method="OnHover"]
