using Godot;
using System;
using System.Collections.Generic;

public partial class ShotgunEnemy : CharacterBody2D
{
	// Constants
	private const float BOUNCE_DAMPING = 0.85f;
	private const float DEBRIS_IMPULSE_MULTIPLIER = 0.25f;
	private const float PROJECTILE_SPAWN_OFFSET = 50.0f;

	[Export]
	private float rotationSpeed = 2.0f; // Adjust this to control how fast the enemy rotates

	[Export]
	private float missilechance = 0.15f; // Adjust this to control how often the enemy shoots missiles

	[Export]
	public PackedScene ProjectileScene { get; set; }

	[Export]
	public PackedScene DebrisScene { get; set; }

	private PackedScene HealthPickupScene { get; set; } = GD.Load<PackedScene>("uid://d1ikyvs5g82jh");
	private PackedScene SpecialHealthPickupScene { get; set; } = GD.Load<PackedScene>("uid://bb3yxnyf1678y");
	private PackedScene SpeedPickupScene { get; set; } = GD.Load<PackedScene>("uid://spsggiksghx");
	private PackedScene BouncePickupScene { get; set; } = GD.Load<PackedScene>("uid://d2uemsath8aqu");
	private PackedScene AcceleratePickupScene { get; set; } = GD.Load<PackedScene>("uid://ckg3p3078vvwj");

	[Export]
	private float moveSpeed = 200.0f; // Adjust this to control movement speed

	[Export]
	private float dodgeSpeedMultiplier = 1.25f; // Speed multiplier when dodging

	[Export]
	private float maintainDistance = 100.0f; // Distance to maintain from player

	[Export]
	private float dodgeDistance = 150.0f; // Distance to retreat when dodging

	[Export]
	private float observationRadius = 300.0f; // Distance at which enemy will stop and observe

	[Export]
	private float observationThreshold = 20.0f; // How close to the observation radius we need to be to stop

	[Export]
	private float positioningSpeed = 0.5f; // Speed multiplier when adjusting observation position

	[Export]
	private float speedWhileTurning = 0.6f; // Speed multiplier while turning

	[Export]
	private int damage = 25;

	[Export]
	private float shootCooldown = 2.0f; // Time between shots in seconds

	[Export]
	private float inertia = 0.5f;

	[Export]
	public float CollisionCooldown = 0.5f; // Cooldown time in seconds between collisions with the same object

	// Collision tracking
	private System.Collections.Generic.Dictionary<Node, float> collisionCooldowns = new System.Collections.Generic.Dictionary<Node, float>(); // Inertia factor to slow space debris from flying off

	[Export]
	private float dropChance = 50.0f; // Chance to drop a health pickup (0-100)

	[Export]
	private int pointReward = 100;

	[Export]
	public bool idle = true;

	private CharacterBody2D player;
	private AudioStreamPlayer2D shootAudio;
	private bool isDodging = false;
	private bool isObserving = false;
	private float targetRotation = 0f;
	private RandomNumberGenerator rng;
	private float shootTimer = 2.0f;
	private Damageable damageable;
	private AnimatedSprite2D deathAnimation;
	private bool shooting = false;
	private int avoid = 0;

	public override void _Ready()
	{
		damageable = GetNode<Damageable>("Damageable");
		shootAudio = GetNode<AudioStreamPlayer2D>("AudioShoot");
		rng = new RandomNumberGenerator();
		rng.Randomize();
		damageable.Death += _OnDamageableDeath;
		deathAnimation = GetNode<AnimatedSprite2D>("DeathSprite");
	}

	public override void _PhysicsProcess(double delta)
	{
		// Update collision cooldowns
		UpdateCollisionCooldowns((float)delta);

		if (player == null)
		{
			var pTemp = GetNode<GameManager>("/root/GameManager").GetPlayerNode();
			if (pTemp != null)
			{
				player = pTemp.GetNode<CharacterBody2D>("PlayerBody");
			}
		}
		if (player != null)
		{
			HandlePlayerCalculations(delta);
		}

		HandleCollisions(delta);
	}

	private void HandlePlayerCalculations(double delta)
	{
		if (idle)
		{
			return;
		}
		Vector2 toPlayer = player.GlobalPosition - GlobalPosition;
		float distanceToPlayer = toPlayer.Length();
		Vector2 directionToPlayer = toPlayer / distanceToPlayer; // Normalize without a separate call

		// Update states and movement based on distance
		UpdateState(distanceToPlayer);

		if (avoid > 0)
		{
			// When avoiding, targetRotation is set in Avoiding function
		}
		else if (!isObserving && avoid == 0)
		{
			float distanceError = distanceToPlayer - observationRadius;
			targetRotation = distanceError < 0
				? Mathf.Atan2(-directionToPlayer.Y, -directionToPlayer.X) + Mathf.Pi / 2  // Face away
				: Mathf.Atan2(directionToPlayer.Y, directionToPlayer.X) + Mathf.Pi / 2;   // Face toward
		}
		else if (!isDodging)
		{
			// When observing, face the player
			targetRotation = Mathf.Atan2(directionToPlayer.Y, directionToPlayer.X) + Mathf.Pi / 2;
		}

		// Calculate turn factor once
		float angleDifference = Mathf.Abs(Mathf.AngleDifference(Rotation, targetRotation));
		float turnFactor = angleDifference / Mathf.Pi;

		// Update rotation
		Rotation = Mathf.LerpAngle(Rotation, targetRotation, rotationSpeed * (float)delta);

		// Calculate movement
		if (!isObserving)
		{
			Vector2 forwardDirection = Vector2.Up.Rotated(Rotation); // Changed from Right to Up
			float speedMultiplier = CalculateSpeedMultiplier(distanceToPlayer, turnFactor);
			Velocity = forwardDirection * moveSpeed * speedMultiplier;
		}
		else
		{
			Velocity = Vector2.Zero;
		}
	}

	private void HandleCollisions(double delta)
	{
		var myVelocity = Velocity;
		var motion = myVelocity * (float)delta;
		var collision = MoveAndCollide(motion);

		if (collision != null)
		{
			var collider = collision.GetCollider();
			var normal = collision.GetNormal();

			if (collider is Node2D node)
			{
				HandleCollisionWithNode(node, myVelocity, normal);
			}
		}
	}

	private void HandleCollisionWithNode(Node2D node, Vector2 velocity, Vector2 normal)
	{
		// Check if this collision is on cooldown
		if (IsCollisionOnCooldown(node))
		{
			// Still apply physics bounce but skip damage
			Velocity = velocity.Bounce(normal) * BOUNCE_DAMPING;
			return;
		}

		// Add collision to cooldown
		AddCollisionCooldown(node);

		if (node is RigidBody2D rigid && rigid is SpaceDebris)
		{
			damageable.TakeDamage(damage);
			rigid.LinearVelocity += velocity * DEBRIS_IMPULSE_MULTIPLIER;
		}

		// Apply bounce to all collision types
		Velocity = velocity.Bounce(normal) * BOUNCE_DAMPING;
	}

	public override void _Process(double delta)
	{
		if (player != null && !isDodging && shooting)
		{
			// Shooting timer
			shootTimer += (float)delta;
			if (shootTimer >= shootCooldown)
			{
				Shoot();
				shootTimer = 0.0f;
			}
		}
	}

	private void UpdateState(float distanceToPlayer)
	{
		if (distanceToPlayer < maintainDistance)
		{
			isObserving = false;
			if (!isDodging)
			{
				StartDodging();
			}
		}
		else if (distanceToPlayer > dodgeDistance)
		{
			isDodging = false;
			isObserving = Mathf.Abs(distanceToPlayer - observationRadius) <= observationThreshold;
		}
	}

	private float CalculateSpeedMultiplier(float distanceToPlayer, float turnFactor)
	{
		float baseMultiplier;
		if (isDodging || avoid > 0)
		{
			baseMultiplier = dodgeSpeedMultiplier;
		}
		else
		{
			baseMultiplier = Mathf.Abs(distanceToPlayer - observationRadius) > observationThreshold * 2
				? 1.0f
				: positioningSpeed;
		}

		return baseMultiplier * Mathf.Lerp(1.0f, speedWhileTurning, turnFactor);
	}

	private void Avoiding(Node2D node)
	{
		avoid += 1;
		isObserving = false;

		// Calculate direction to the debris
		Vector2 toDebris = node.GlobalPosition - GlobalPosition;
		float distanceToDebris = toDebris.Length();

		// Avoid division by zero
		if (distanceToDebris == 0)
			return;

		Vector2 directionToDebris = toDebris / distanceToDebris;

		// Determine which side to avoid (perpendicular to debris direction)
		// Use cross product to determine if we should go left or right
		Vector2 currentDirection = Vector2.Up.Rotated(Rotation);
		float crossProduct = currentDirection.X * directionToDebris.Y - currentDirection.Y * directionToDebris.X;

		// Positive cross product means debris is on the right, so dodge left
		// Negative cross product means debris is on the left, so dodge right
		float avoidanceAngle = crossProduct > 0 ? -Mathf.Pi / 2 : Mathf.Pi / 2;

		// Calculate avoidance direction (perpendicular to debris direction)
		float avoidanceRotation = Mathf.Atan2(directionToDebris.Y, directionToDebris.X) + avoidanceAngle;

		// Set target rotation to the avoidance direction
		targetRotation = avoidanceRotation + Mathf.Pi / 2;
	}

	private void NotAvoiding(Node2D node)
	{
		avoid -= 1;
	}

	private void _on_check_timeout()
	{
		GetNode<Area2D>("Bumper").Monitoring = false;
		avoid = 0;
		GetNode<Area2D>("Bumper").Monitoring = true;
	}

	private void StartDodging()
	{
		isDodging = true;

		// Pick a random perpendicular direction (left or right)
		bool dodgeLeft = rng.Randf() > 0.5f;
		float dodgeOffset = dodgeLeft ? Mathf.Pi / 2 : -Mathf.Pi / 2;

		// Add some randomness to the dodge angle (±30 degrees)
		float randomVariation = rng.RandfRange(-Mathf.Pi / 6, Mathf.Pi / 6);

		// Calculate dodge direction relative to current rotation, accounting for 90-degree offset
		targetRotation = Rotation + dodgeOffset + randomVariation;
	}

	private void InRange(Node body)
	{
		if (body is Player)
		{
			shooting = true;
			idle = false;
		}
	}

	private void OutRange(Node body)
	{
		if (body is Player)
		{
			shooting = false;
			shootTimer = 2.0f;
		}
	}

	private void Shoot()
	{
		if (ProjectileScene != null)
		{
			for (int i = 0; i < 5; i++)
			{
				// Create projectile instance
				Projectile projectile = (Projectile)ProjectileScene.Instantiate();

				float pelletOffSet = 1 - i * 0.5f;
				// Updated spawn offset calculation to match the new rotation
				Vector2 spawnOffset = Vector2.Up.Rotated(Rotation) * (PROJECTILE_SPAWN_OFFSET + 25);
				projectile.GlobalPosition = GlobalPosition + spawnOffset;
				projectile.Rotation = Rotation;
				GetParent().AddChild(projectile);
				projectile.fireBullet(Rotation - pelletOffSet);
			}
			// Play shoot sound
			shootAudio?.Play();
		}
	}

	private void PlayExplosionSound()
	{
		var explosionAudio = GetNode<AudioStreamPlayer2D>("AudioExplode");
		if (explosionAudio?.Stream != null)
		{
			// Create a persistent audio player that survives enemy destruction
			AudioStreamPlayer2D persistentAudio = new AudioStreamPlayer2D
			{
				Stream = explosionAudio.Stream,
				GlobalPosition = GlobalPosition,
				VolumeDb = explosionAudio.VolumeDb,
				PitchScale = explosionAudio.PitchScale
			};

			GetParent().AddChild(persistentAudio);
			persistentAudio.Play();
			persistentAudio.Finished += () => persistentAudio.QueueFree();
		}
	}

	public void _OnDamageableDeath()
	{
		SetProcess(false);
		// Start death animation
		deathAnimation.Visible = true;
		deathAnimation.Play();
		GetNode<Sprite2D>("Sprite2D").Visible = false;

		// Play explosion sound
		PlayExplosionSound();

		// Increase score
		GetNode<GameManager>("/root/GameManager").Score += pointReward; // Adjust point value as needed
	}
	
	public void _on_death_sprite_animation_finished()
	{
		Random rand = new Random();
		if (rand.Next(0, 100) < dropChance)
		{
			int rando = rand.Next(0, 100);
			Area2D pickUp;
			if (rando < 20)
			{
				pickUp = (SpeedPickUp)SpeedPickupScene.Instantiate();
			}
			else if (rando > 19 && rando < 40)
			{
				pickUp = (AcceleratePickUp)AcceleratePickupScene.Instantiate();
			}
			else if (rando > 39 && rando < 60)
			{
				pickUp = (BouncePickUp)BouncePickupScene.Instantiate();
			}
			else if (rando > 69 && rando < 80)
			{
				pickUp = (SpecialHealthPickup)SpecialHealthPickupScene.Instantiate();
			}
			else
			{
				pickUp = (HealthPickup)HealthPickupScene.Instantiate();
			}

			pickUp.GlobalPosition = GlobalPosition;
			Callable.From(() => GetParent().AddChild(pickUp)).CallDeferred();
		}
		// Create space debris and clean up
		Callable.From(() =>
		{
			SpaceDebris deadSprite = (SpaceDebris)DebrisScene.Instantiate();
			Vector2 spawnOffset = Vector2.Up.Rotated(Rotation);

			deadSprite.GlobalPosition = GlobalPosition + spawnOffset;
			deadSprite.ApplyImpulse(Vector2.Up.Rotated(Rotation) * Velocity.Length() * inertia);
			deadSprite.Rotation = Rotation;

			GetParent().AddChild(deadSprite);
			QueueFree();
		}).CallDeferred();
	}

	// Collision cooldown management methods
	private void UpdateCollisionCooldowns(float delta)
	{
		var keysToRemove = new List<Node>();
		var keysToUpdate = new List<Node>(collisionCooldowns.Keys);

		foreach (var node in keysToUpdate)
		{
			if (!IsInstanceValid(node))
			{
				keysToRemove.Add(node);
				continue;
			}

			collisionCooldowns[node] -= delta;
			if (collisionCooldowns[node] <= 0)
			{
				keysToRemove.Add(node);
			}
		}

		foreach (var key in keysToRemove)
		{
			collisionCooldowns.Remove(key);
		}
	}

	private bool IsCollisionOnCooldown(Node node)
	{
		return collisionCooldowns.ContainsKey(node) && collisionCooldowns[node] > 0;
	}

	private void AddCollisionCooldown(Node node)
	{
		collisionCooldowns[node] = CollisionCooldown;
	}
}
