using Godot;
using System;

public partial class Score : Control
{
    private Label ScoreLabel;
    private GameManager MyGameManager;
    private PlayerCamera PlayerCameraNode;

    public override void _Ready()
    {
        this.Visible = false;
        ScoreLabel = GetNode<Label>("HSplitContainer/ScoreCount");
        MyGameManager = GetNode<GameManager>("/root/GameManager");
        MyGameManager.Connect(GameManager.SignalName.ScoreChanged, new Callable(this, MethodName.OnScoreChanged));
        OnScoreChanged(MyGameManager.Score);
    }

    public void OnScoreChanged(int newScore)
    {
        ScoreLabel.Text = newScore.ToString();
    }
}
