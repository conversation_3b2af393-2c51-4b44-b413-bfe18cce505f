using Godot;
using System;

public partial class Planet : StaticBody2D
{
    [Export]
    private Texture2D PlanetTexture1 { get; set; }
    [Export]
    private Texture2D PlanetTexture2 { get; set; }
    [Export]
    private Texture2D PlanetTexture3 { get; set; }
    [Export]
    private Texture2D PlanetTexture4 { get; set; }
    [Export]
    private Texture2D PlanetTexture5 { get; set; }
    [Export]
    private Texture2D PlanetTexture6 { get; set; }
    [Export]
    private Texture2D PlanetTexture7 { get; set; }
    [Export]
    private Texture2D PlanetTexture8 { get; set; }
    
    private Sprite2D planetSprite;
    
    public override void _Ready()
    {

        planetSprite = GetNode<Sprite2D>("Sprite2D");

        var rng = new RandomNumberGenerator();
        rng.Randomize();
        var randomNumber = rng.RandiRange(1, 8);
        if (randomNumber == 1)
        {
            planetSprite.Texture = PlanetTexture1;
            Scale = new Vector2(10, 10);
        }
        else if (randomNumber == 2)
        {
            planetSprite.Texture = PlanetTexture2;
            Scale = new Vector2(7, 7);
        }
        else if (randomNumber == 3)
        {
            planetSprite.Texture = PlanetTexture3;
            Scale = new Vector2(15, 15);
        }
        else if (randomNumber == 4)
        {
            planetSprite.Texture = PlanetTexture4;
            Scale = new Vector2(10, 10);
        }
        else if (randomNumber == 5)
        {
            planetSprite.Texture = PlanetTexture5;
            Scale = new Vector2(12, 12);
        }
        else if (randomNumber == 6)
        {
            planetSprite.Texture = PlanetTexture6;
            planetSprite.Scale = new Vector2(1.28f, 1.28f);
            Scale = new Vector2(10, 10);
        }
        else if (randomNumber == 7)
        {
            planetSprite.Texture = PlanetTexture7;
            planetSprite.Scale = new Vector2(1.28f, 1.28f);
            Scale = new Vector2(20, 20);
        }
        else if (randomNumber == 8)
        {
            planetSprite.Texture = PlanetTexture8;
            planetSprite.Scale = new Vector2(1.29f, 1.29f);
            Scale = new Vector2(8, 8);
        }
        Rotation = rng.RandfRange(1, 360);
    }
}
