using Godot;
using System;
using System.ComponentModel;

public partial class Missile : RigidBody2D
{
	[Export]
	private float speed = 20.0f;
	[Export]
	private float maxSpeed = 450.0f;
	[Export]
	private float lifetime = 5.0f;
	[Export]
	private float timer = 0.0f;
	[Export]
	private int damage = 10;
	[Export]
	private int explosionForce = 500;
	[Export]
	private float turnSpeed = 0.25f; // How quickly the missile can turn

	private CharacterBody2D player;
	private Vector2 direction;
	private Vector2 visualDirection;
	private Sprite2D missileVisual;
	private Vector2 myVelocity;
	private Damageable damageable;

	public override void _Ready()
	{
		missileVisual = GetNode<Sprite2D>("Missile");
		var pTemp = GetNode<GameManager>("/root/GameManager").GetPlayerNode();
		if (pTemp != null)
		{
			player = pTemp.GetNode<CharacterBody2D>("PlayerBody");
		}	
		visualDirection = direction; // Initialize visualDirection with the same value as direction
		damageable = GetNode<Damageable>("Damageable");
		damageable.Death += _OnDamageableDeath;
	}

	public void fireMissile(float rotation)
	{
		// Calculate direction based on rotation
		direction = new Vector2(Mathf.Sin(rotation), -Mathf.Cos(rotation));
		// Initialize visualDirection with the same direction
		visualDirection = direction;
		// Apply impulse in the direction of travel
		ApplyImpulse(direction * (speed * 25));

		// Set initial rotation of the visual
		missileVisual.Rotation = rotation;
		GetNode<CollisionShape2D>("MissileHitBox").Rotation = missileVisual.Rotation;
	}

	public override void _Process(double delta)
	{
		if (player != null && timer > 1.5f)
		{
			// Calculate direction to player
			Vector2 toPlayer = player.GlobalPosition - GlobalPosition;
			Vector2 targetDirection = toPlayer.Normalized();

			// Gradually adjust direction toward player with limited turn radius
			direction = direction.Normalized();
			visualDirection = visualDirection.Normalized();
			visualDirection = visualDirection.Lerp(targetDirection, 0.05f);
			direction = direction.Lerp(targetDirection, turnSpeed);

			// change the visual rotation of the sprite
			missileVisual.Rotation = visualDirection.Angle() - 300f; //idk why I have to -300f but it works
			GetNode<CollisionShape2D>("MissileHitBox").Rotation = missileVisual.Rotation;
		}
		if (timer >= 7f)
		{
			//missile becomes deadly to friends after a moment
			CollisionMask = 11;
		}
		// Increase speed over time for a missile-like effect
		speed += 0.15f;
		if (speed > maxSpeed)
		{
			speed = maxSpeed;

		}
		myVelocity = LinearVelocity;

		if (direction.X > 0 && LinearVelocity.X < 0)
		{
			myVelocity.X *= 0.98f;

		}
		if (direction.X < 0 && LinearVelocity.X > 0)
		{
			myVelocity.X *= 0.98f;
		}
		if (direction.Y > 0 && LinearVelocity.Y < 0)
		{
			myVelocity.Y *= 0.98f;
		}
		if (direction.Y < 0 && LinearVelocity.Y > 0)
		{
			myVelocity.Y *= 0.98f;
		}

		if (myVelocity.X > maxSpeed)
		{
			myVelocity.X = maxSpeed;
		}
		if (myVelocity.X < -maxSpeed)
		{
			myVelocity.X = -maxSpeed;
		}
		if (myVelocity.Y > maxSpeed)
		{
			myVelocity.Y = maxSpeed;
		}
		if (myVelocity.Y < -maxSpeed)
		{
			myVelocity.Y = -maxSpeed;
		}
		LinearVelocity = myVelocity;
		ApplyImpulse(direction * speed * (float)delta);
		timer += (float)delta;
		// Destroy projectile after lifetime
		if (timer >= lifetime)
		{
			Explosion();
		}
	}

	private void OnBodyEntered(Node body)
	{
		if (body is not Projectile && body is not Missile && timer > 1.5f)
		{
			Explosion();
			// Destroy projectile after hitting something
		}
		//player able to destroy unactivated missiles
		else if (body is Player)
		{
			QueueFree();
		}
	}

	private void Explosion()
	{
		GetNode<Sprite2D>("Explosion").Visible = true;
		GetNode<Area2D>("ExplosionArea").Monitoring = true;
		if (timer >= lifetime + 0.1f)
		{
			QueueFree();
		}
	}

	private void OnExplosionBodyEntered(Node body)
	{
		if (body is Node2D)
		{
			var targetDamageable = body.GetNodeOrNull<Damageable>("Damageable");
			if (targetDamageable != null)
			{
				targetDamageable.TakeDamage(damage);
			}
			var targetMovable = body.GetNodeOrNull<MovableObject>("MovableObject");
			if (targetMovable != null)
			{
				targetMovable.Pushed(explosionForce, GlobalPosition);
			}
		}
		QueueFree();
	}

	public void _OnDamageableDeath()
	{
		Explosion();
	}
}





