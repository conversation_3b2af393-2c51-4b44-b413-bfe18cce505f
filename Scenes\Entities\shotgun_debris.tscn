[gd_scene load_steps=5 format=3 uid="uid://bmbgjnvqkamyq"]

[ext_resource type="Script" uid="uid://crtpthkvbit8o" path="res://scripts/Entities/SpaceDebris.cs" id="1_clcas"]
[ext_resource type="Texture2D" uid="uid://cjr324boput30" path="res://Assets/Entities/ShotgunDeath.png" id="3_8bmsm"]
[ext_resource type="Script" uid="uid://dgshvmje4dtpo" path="res://scripts/Nodes/MovableObject.cs" id="4_yld8b"]

[sub_resource type="CircleShape2D" id="CircleShape2D_txmfh"]
radius = 33.0

[node name="ShotgunDebris" type="RigidBody2D"]
collision_layer = 8
collision_mask = 8
contact_monitor = true
max_contacts_reported = 3
script = ExtResource("1_clcas")
debrisTexture1 = ExtResource("3_8bmsm")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(-3, 0)
shape = SubResource("CircleShape2D_txmfh")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(2.3125, 2.3125)

[node name="MovableObject" type="Node2D" parent="."]
script = ExtResource("4_yld8b")
metadata/_custom_type_script = "uid://dgshvmje4dtpo"
