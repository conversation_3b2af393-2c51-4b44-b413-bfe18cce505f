using Godot;
using System;
using System.Collections.Generic;

public partial class SpaceDebris : RigidBody2D
{

	private PackedScene HealthPickupScene { get; set; } = GD.Load<PackedScene>("uid://d1ikyvs5g82jh");
	private PackedScene SpecialHealthPickupScene { get; set; } = GD.Load<PackedScene>("uid://bb3yxnyf1678y");
	private PackedScene SpeedPickupScene { get; set; } = GD.Load<PackedScene>("uid://spsggiksghx");
	private PackedScene BouncePickupScene { get; set; } = GD.Load<PackedScene>("uid://d2uemsath8aqu");
	private PackedScene AcceleratePickupScene { get; set; } = GD.Load<PackedScene>("uid://ckg3p3078vvwj");

	[Export]
	private float dropChance = 10.0f; // Chance to drop a health pickup (0-100)
	[Export]
	private bool isMovable = true;

	[Export]
	private float dampingFactor = 0.999f;

	[Export]
	private bool generalSpaceDebris = false;

	[Export]
	private Texture2D debrisTexture1 { get; set; }
	[Export]
	private Texture2D debrisTexture2 { get; set; }
	[Export]
	private Texture2D debrisTexture3 { get; set; }
	[Export]
	private Texture2D debrisTexture4 { get; set; }
	[Export]
	private Texture2D debrisTexture5 { get; set; }

	//[Export]
	//private float mass = 10f;

	[Export]
	private bool useInertia = true;

	private Sprite2D debrisSprite;
	private CollisionShape2D debrisSize;
	private Vector2 scaling = new Vector2(1, 1);
	private bool hit = false;

	public override void _Ready()
	{
		GravityScale = 0.0f;
		CanSleep = false;
		Freeze = false;
		LockRotation = false;
		LinearDamp = 0.05f;

		debrisSprite = GetNode<Sprite2D>("Sprite2D");
		debrisSize = GetNode<CollisionShape2D>("CollisionShape2D");

		if (generalSpaceDebris)
		{
			var rng = new RandomNumberGenerator();
			rng.Randomize();
			var randomNumber = rng.RandiRange(1, 5);
			if (randomNumber == 1)
			{
				debrisSprite.Texture = debrisTexture1;
				debrisSprite.Scale = new Vector2(5, 5);
				var rectangleShape = new RectangleShape2D();
				rectangleShape.Size = new Vector2(55, 55); // Increased size
				debrisSize.Shape = rectangleShape;
				debrisSize.Scale = new Vector2(2, 2);

				dampingFactor = 0.99f;
			}
			else if (randomNumber == 2)
			{
				debrisSprite.Texture = debrisTexture2;
			}
			else if (randomNumber == 3)
			{
				debrisSprite.Texture = debrisTexture3;
				debrisSprite.Scale = new Vector2(4, 4);
				var rectangleShape = new RectangleShape2D();
				rectangleShape.Size = new Vector2(55, 55); // Increased size
				debrisSize.Shape = rectangleShape;
				debrisSize.Scale = new Vector2(2, 2);
			}
			else if (randomNumber == 4)
			{
				debrisSprite.Texture = debrisTexture4;
				debrisSprite.Scale = new Vector2(8, 8);
				debrisSize.Scale = new Vector2(3.5f, 3.5f);
				dampingFactor = 0.97f;
			}
			else if (randomNumber == 5)
			{
				debrisSprite.Texture = debrisTexture5;
				scaling = new Vector2(0.5f, 0.5f);
			}
			Rotation = rng.RandfRange(1, 360);
		}
		else
		{
			debrisSprite.Texture = debrisTexture1;
		}
	}

	public override void _PhysicsProcess(double delta)
	{
		if (Scale != scaling)
		{
			Scale = scaling;
		}

		if (isMovable && LinearVelocity != Vector2.Zero)
		{
			// Apply damping to slow down over time
			LinearVelocity *= dampingFactor;
		}

		var motion = LinearVelocity * (float)delta; //time since last frame
		var collision_detect = MoveAndCollide(motion);
		if (collision_detect != null)
		{
			var collider = collision_detect.GetCollider();
			var collision_normal = collision_detect.GetNormal();
			if (collider is Node2D node)
			{
				if (node is StaticBody2D)
				{
					LinearVelocity = LinearVelocity.Bounce(collision_normal);
				}
				else if (node is CharacterBody2D)
				{
					LinearVelocity = LinearVelocity.Bounce(collision_normal);
				}
			}
		}
	}

	private void _on_area_2d_body_entered(Node node)
	{
		
		if (!hit && node is Player)
		{
			hit = true;
			Random rand = new Random();
			if (rand.Next(0, 100) < dropChance)
			{
				int rando = rand.Next(0, 100);
				Area2D pickUp;
				if (rando < 20)
				{
					pickUp = (SpeedPickUp)SpeedPickupScene.Instantiate();
				}
				else if (rando > 19 && rando < 40)
				{
					pickUp = (AcceleratePickUp)AcceleratePickupScene.Instantiate();
				}
				else if (rando > 39 && rando < 60)
				{
					pickUp = (BouncePickUp)BouncePickupScene.Instantiate();
				}
				else if (rando > 69 && rando < 80)
				{
					pickUp = (SpecialHealthPickup)SpecialHealthPickupScene.Instantiate();
				}
				else
				{
					pickUp = (HealthPickup)HealthPickupScene.Instantiate();
				}

				pickUp.GlobalPosition = GlobalPosition;
				Callable.From(() => GetParent().AddChild(pickUp)).CallDeferred();
			}
		}
	}
}

