using Godot;
using Godot.Collections;
using System;
using System.Collections.Generic;

public partial class Player : CharacterBody2D
{
	//Properties
	Vector2 screenSize;
	private Damageable damageable;
	[Export]
	private int Damage = 25; // Damage on Hit
	[Export]
	public float PushForce = 4; // Velocity When Input Pressed
	[Export]
	public float ExcelPushForce = 3; // Extra Velocity when Switching Directions
	[Export]
	public float Friction = 0.995f; // Friction coefficient - higher values = less friction
	[Export]
	public Vector2 MaxInputSpeed = new Vector2(1000, 1000); // Max Speed (Not Hard Limit)
	[Export]
	public int MaxSpeedMultiplier = 2; //multiply this with MaxInputSpeed to get actual hard limit set, extra friction added at half
	[Export]
	public float ExtraFriction = 0.95f; // Extra Friction When Over MaxInputSpeed
	[Export]
	public float HealthScaleFactor = 0.1f; // How much to scale per health point increase
	public float bounceFallOff = 0.85f; // How much to reduce velocity when bouncing off something

	[Export]
	public float CollisionCooldown = 0.5f; // Cooldown time in seconds between collisions with the same object
	[Export]
	public int winScore = 10000;

	// Collision tracking
	private System.Collections.Generic.Dictionary<Node, float> collisionCooldowns = new System.Collections.Generic.Dictionary<Node, float>();

	//Methods
	public override void _Ready()
	{
		screenSize = GetViewportRect().Size;
		damageable = GetNode<Damageable>("Damageable");
		damageable.Death += _OnDamageableDeath;
		damageable.MaxHealthChanged += _OnMaxHealthChanged;
		GetNode<GameManager>("/root/GameManager").SetPlayerNode(this.GetParent());
	}
	
	public override void _PhysicsProcess(double delta)
	{
		//Win Condition
		if (GetNode<GameManager>("/root/GameManager").Score >= winScore)
		{
			GetTree().ChangeSceneToFile("uid://cik3pevurwvmm");
		}

		// Update collision cooldowns
		UpdateCollisionCooldowns((float)delta);

		bool inputDetectedX = false;
		bool inputDetectedY = false;

		var myVelocity = Velocity;
		if (Input.IsActionPressed("move_right"))
		{
			if (Velocity.X < 0)
			{
				myVelocity.X += PushForce * ExcelPushForce;
			}
			else { myVelocity.X += PushForce; }
			inputDetectedX = true;
		}
		// -- move left --
		if (Input.IsActionPressed("move_left"))
		{
			if (Velocity.X > 0)
			{
				myVelocity.X -= PushForce * ExcelPushForce;
			}
			else { myVelocity.X -= PushForce; }
			inputDetectedX = true;
		}
		// -- move down --
		if (Input.IsActionPressed("move_down"))
		{
			if (Velocity.Y < 0)
			{
				myVelocity.Y += PushForce * ExcelPushForce;
			}
			else { myVelocity.Y += PushForce; }
			inputDetectedY = true;
		}
		// -- move up --
		if (Input.IsActionPressed("move_up"))
		{
			if (Velocity.Y > 0)
			{
				myVelocity.Y -= PushForce * ExcelPushForce;
			}
			else { myVelocity.Y -= PushForce; }
			inputDetectedY = true;
		}

		// -- stop --
		if (Input.IsActionPressed("stop"))
		{
			if (Velocity.X > 0)
			{
				myVelocity.X -= PushForce * ExcelPushForce;
				if (myVelocity.X < PushForce * ExcelPushForce)
				{
					myVelocity.X = 0;
				}
			}
			else if (Velocity.X < 0)
			{
				myVelocity.X += PushForce * ExcelPushForce;
				if (myVelocity.X > -PushForce * ExcelPushForce)
				{
					myVelocity.X = 0;
				}
			}
			if (Velocity.Y > 0)
			{
				myVelocity.Y -= PushForce * ExcelPushForce;
				if (myVelocity.Y < PushForce * ExcelPushForce)
				{
					myVelocity.Y = 0;
				}
			}
			else if (Velocity.Y < 0)
			{
				myVelocity.Y += PushForce * ExcelPushForce;
				if (myVelocity.Y > -PushForce * ExcelPushForce)
				{
					myVelocity.Y = 0;
				}
			}
		}

		// -- max speed --
		if (myVelocity.X > 0 && myVelocity.X > MaxInputSpeed.X)
		{
			if (myVelocity.X > MaxInputSpeed.X * MaxSpeedMultiplier / 2)
			{
				myVelocity.X *= ExtraFriction;
			}
			if (myVelocity.X > MaxInputSpeed.X * MaxSpeedMultiplier)
			{
				myVelocity.X = MaxInputSpeed.X * MaxSpeedMultiplier;
			}
			myVelocity.X *= Friction;
			myVelocity.X -= 6;

		}
		else if (myVelocity.X < 0 && myVelocity.X < -MaxInputSpeed.X)
		{
			if (myVelocity.X < -MaxInputSpeed.X * MaxSpeedMultiplier / 2)
			{
				myVelocity.X *= ExtraFriction;
			}
			if (myVelocity.X < -MaxInputSpeed.X * MaxSpeedMultiplier)
			{
				myVelocity.X = -MaxInputSpeed.X * MaxSpeedMultiplier;
			}
			myVelocity.X *= Friction;
			myVelocity.X += 6;

		}
		if (myVelocity.Y > 0 && myVelocity.Y > MaxInputSpeed.Y)
		{
			if (myVelocity.Y > MaxInputSpeed.Y * MaxSpeedMultiplier / 2)
			{
				myVelocity.Y *= ExtraFriction;
			}
			if (myVelocity.Y > MaxInputSpeed.Y * MaxSpeedMultiplier)
			{
				myVelocity.Y = MaxInputSpeed.Y * MaxSpeedMultiplier;
			}
			myVelocity.Y *= Friction;
			myVelocity.Y -= 6;

		}
		else if (myVelocity.Y < 0 && myVelocity.Y < -MaxInputSpeed.Y)
		{
			if (myVelocity.Y < -MaxInputSpeed.Y * MaxSpeedMultiplier / 2)
			{
				myVelocity.Y *= ExtraFriction;
			}
			if (myVelocity.Y < -MaxInputSpeed.Y * MaxSpeedMultiplier)
			{
				myVelocity.Y = -MaxInputSpeed.Y * MaxSpeedMultiplier;
			}
			myVelocity.Y *= Friction;
			myVelocity.Y += 6;

		}

		// -- friction --
		if (!inputDetectedX)
		{
			myVelocity.X *= Friction;
		}
		if (!inputDetectedY)
		{
			myVelocity.Y *= Friction;
		}

		Velocity = myVelocity;

		var motion = myVelocity * (float)delta; //time since last frame
		var collisionDetect = MoveAndCollide(motion);

		if (collisionDetect != null)
		{
			var collider = collisionDetect.GetCollider();
			var collisionNormal = collisionDetect.GetNormal();
			if (collider is Node2D node)
			{
				// Check if this collision is on cooldown
				if (IsCollisionOnCooldown(node))
				{
					// Still apply physics bounce but skip damage
					ApplyCollisionPhysics(node, myVelocity, collisionNormal);
					return;
				}

				// Add collision to cooldown
				AddCollisionCooldown(node);

				var targetDamageable = node.GetNodeOrNull<Damageable>("Damageable");
				if (targetDamageable != null)
				{
					targetDamageable.TakeDamage(Damage);
				}

				//Apply physics collision effects
				ApplyCollisionPhysics(node, myVelocity, collisionNormal);

				if (node is RigidBody2D rigid)
				{
					if (rigid is SpaceDebris)
					{
						Velocity = myVelocity.Bounce(collisionNormal);
						Velocity *= bounceFallOff;

						rigid.LinearVelocity += myVelocity * 0.25f;
					}
				}
				else if (node is StaticBody2D)
				{
					Velocity = myVelocity.Bounce(collisionNormal);
					Velocity *= bounceFallOff;
				}
				else if (node is CharacterBody2D character)
				{
					Velocity = myVelocity.Bounce(collisionNormal);
					Velocity *= bounceFallOff;
				}
			}
		}
	}

		// -- spinning --
	public override void _Process(double delta)
	{
		Sprite2D asteroidPicture = GetNode<Sprite2D>("AsteroidTemp");
		asteroidPicture.Rotation += (float)((delta * Math.Sqrt(Math.Pow(Velocity.X, 2) + Math.Pow(Velocity.Y, 2)) / 200));
	}

	public void _OnDamageableDeath()
	{
		Callable.From(() => GetTree().ChangeSceneToFile("uid://d4r4r3kx86o6")).CallDeferred();
	}

	public void _OnMaxHealthChanged(int newMaxHealth, int oldMaxHealth)
	{
		// Calculate the ratio of new health to old health
		float healthRatio = (float)newMaxHealth / (float)oldMaxHealth;
		
		// Scale the player based on the health ratio and scale factor
		// Current scale multiplied by the health ratio adjusted by the scale factor
		float scaleMultiplier = 1.0f + ((healthRatio - 1.0f) * HealthScaleFactor);
		Vector2 newScale = Scale * scaleMultiplier;
		
		// Apply the new scale to the player
		Scale = newScale;
	}

	// Collision cooldown management methods
	private void UpdateCollisionCooldowns(float delta)
	{
		var keysToRemove = new List<Node>();
		var keysToUpdate = new List<Node>(collisionCooldowns.Keys);

		foreach (var node in keysToUpdate)
		{
			if (!IsInstanceValid(node))
			{
				keysToRemove.Add(node);
				continue;
			}

			collisionCooldowns[node] -= delta;
			if (collisionCooldowns[node] <= 0)
			{
				keysToRemove.Add(node);
			}
		}

		foreach (var key in keysToRemove)
		{
			collisionCooldowns.Remove(key);
		}
	}

	private bool IsCollisionOnCooldown(Node node)
	{
		return collisionCooldowns.ContainsKey(node) && collisionCooldowns[node] > 0;
	}

	private void AddCollisionCooldown(Node node)
	{
		collisionCooldowns[node] = CollisionCooldown;
	}

	private void ApplyCollisionPhysics(Node2D node, Vector2 myVelocity, Vector2 collisionNormal)
	{
		if (node is RigidBody2D rigid)
		{
			if (rigid is SpaceDebris)
			{
				Velocity = myVelocity.Bounce(collisionNormal);
				Velocity *= 0.85f;

				rigid.LinearVelocity += myVelocity * 0.25f;
			}
		}
		else if (node is StaticBody2D)
		{
			Velocity = myVelocity.Bounce(collisionNormal);
			Velocity *= bounceFallOff;
		}
		else if (node is CharacterBody2D character)
		{
			Velocity = myVelocity.Bounce(collisionNormal);
			Velocity *= bounceFallOff;
		}
	}
}
