using Godot;
using System;

public partial class GameManager : Node
{
	// Other global variables...

	//Score Tracking Signal
	[Signal]
    public delegate void ScoreChangedEventHandler(int newScore);

	// Reference to the pause menu scene
	private PackedScene _pauseMenuScene;
	public static bool IsPaused = false;
	public int myscore = 0;
	private Node PlayerNode = null;
	private Vector2 PlayerCameraZoom = new Vector2(1f, 1f); 

	public override void _Ready()
	{
		ProcessMode = Node.ProcessModeEnum.Always;
		// Load the pause menu scene
		_pauseMenuScene = GD.Load<PackedScene>("uid://hqrw4ih0y1fp");
	}
	//Score Tracking
	public int Score
    { 
        get => myscore;
		set
		{
			myscore = value;
			EmitSignal(SignalName.ScoreChanged, myscore);
        }
    }

	public override void _Input(InputEvent @event)
	{
		if (@event.IsActionPressed("ui_cancel")) // ESC is mapped to ui_cancel by default
		{
			TogglePauseMenu();
		}
	}
	
	public void TogglePauseMenu()
	{
		IsPaused = !IsPaused;

		if (IsPaused)
		{
			// Pause the game and only Gameplay
			GetTree().Paused = true;
			// Instance and show the pause menu
			Control pauseMenu = _pauseMenuScene.Instantiate<Control>();
			GetTree().Root.AddChild(pauseMenu);
			if (PlayerNode != null)
			{
				PlayerCamera playerCameraNode = PlayerNode.GetNode<PlayerCamera>("PlayerCamera");
				Vector2 viewportSize = playerCameraNode.GetViewportRect().Size;
				Vector2 topLeft = playerCameraNode.GetScreenCenterPosition() - (viewportSize / 2);
				pauseMenu.Position = topLeft;
				PlayerCameraZoom = playerCameraNode.Zoom;
				playerCameraNode.Zoom = new Vector2(1f, 1f);
			}
			else
			{
				pauseMenu.Position = new Vector2(-964, -540);
			}
		}
		else
		{
			// Unpause the game
			GetTree().Paused = false;
			// Remove the pause menu
			Node pauseMenu = GetTree().Root.GetNode("MainMenu");
			pauseMenu.QueueFree();

			if (PlayerNode != null)
			{
				PlayerCamera playerCameraNode = PlayerNode.GetNode<PlayerCamera>("PlayerCamera");
				playerCameraNode.Zoom = PlayerCameraZoom;
			}
		}
	}

	public void SetPlayerNode(Node p)
	{
		PlayerNode = p;
	}

	public Node GetPlayerNode()
	{
		return PlayerNode;
	}
}
