[gd_scene load_steps=10 format=3 uid="uid://simevktmm780"]

[ext_resource type="Script" uid="uid://crtpthkvbit8o" path="res://scripts/Entities/SpaceDebris.cs" id="1_gmnal"]
[ext_resource type="Texture2D" uid="uid://bu7rj8qnjm4x5" path="res://Assets/Entities/Debris1.png" id="2_ymtk2"]
[ext_resource type="Script" uid="uid://dgshvmje4dtpo" path="res://scripts/Nodes/MovableObject.cs" id="3_dtjs7"]
[ext_resource type="Texture2D" uid="uid://celr3f85ivyfr" path="res://Assets/Entities/Debris2.png" id="3_xjkdi"]
[ext_resource type="Texture2D" uid="uid://bmqwi8uyk77ad" path="res://Assets/Entities/Debris3.png" id="4_xvr2p"]
[ext_resource type="Texture2D" uid="uid://njw4235157h8" path="res://Assets/Entities/Debris4.png" id="5_63q33"]
[ext_resource type="Texture2D" uid="uid://b5plm5ssyufbm" path="res://Assets/Entities/Debris5.png" id="6_6podq"]

[sub_resource type="CircleShape2D" id="CircleShape2D_txmfh"]
radius = 33.0

[sub_resource type="CircleShape2D" id="CircleShape2D_xjkdi"]
radius = 38.5998

[node name="SpaceDebris" type="RigidBody2D"]
scale = Vector2(1.01071, 0.995567)
collision_layer = 8
collision_mask = 24
contact_monitor = true
max_contacts_reported = 3
script = ExtResource("1_gmnal")
dropChance = 25.0
generalSpaceDebris = true
debrisTexture1 = ExtResource("2_ymtk2")
debrisTexture2 = ExtResource("3_xjkdi")
debrisTexture3 = ExtResource("4_xvr2p")
debrisTexture4 = ExtResource("5_63q33")
debrisTexture5 = ExtResource("6_6podq")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(-3, 0)
shape = SubResource("CircleShape2D_txmfh")

[node name="Area2D" type="Area2D" parent="CollisionShape2D"]
position = Vector2(3, 0)

[node name="CollisionShape2D" type="CollisionShape2D" parent="CollisionShape2D/Area2D"]
position = Vector2(-2.96821, 0)
shape = SubResource("CircleShape2D_xjkdi")
debug_color = Color(0.837666, 0.00121251, 0.928414, 0.42)

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(2.3125, 2.3125)

[node name="MovableObject" type="Node2D" parent="."]
script = ExtResource("3_dtjs7")
metadata/_custom_type_script = "uid://dgshvmje4dtpo"

[connection signal="body_entered" from="CollisionShape2D/Area2D" to="." method="_on_area_2d_body_entered"]
