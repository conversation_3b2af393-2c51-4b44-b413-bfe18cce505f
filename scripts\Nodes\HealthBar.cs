using Godot;
using System;

[GlobalClass]
public partial class HealthBar : ColorRect
{
	[Export]
	private bool IsPlayerHealthBar = false;
	[Export]
	private float HealthScaleFactor = 0.25f;
	private Damageable DamageableNode;
	private PlayerCamera PlayerCameraNode;

	public override void _Ready()
	{
		if (IsPlayerHealthBar)
		{
			this.Visible = false;
		}
	}

	public override void _Process(double delta)
	{
		if (IsPlayerHealthBar)
		{
			if (DamageableNode == null || PlayerCameraNode == null) // Assign Nodes
			{
				var playerNode = GetNode<GameManager>("/root/GameManager").GetPlayerNode();
				if (DamageableNode == null)
				{
					DamageableNode = playerNode.GetNode<Damageable>("PlayerBody/Damageable");
					if (DamageableNode != null)
					{
						DamageableNode.HealthChanged += OnHealthChanged;
						DamageableNode.MaxHealthChanged += OnMaxHealthChanged;
					}
				}
			}
		}
	}

	private void OnHealthChanged(int currentHealth, int maxHealth)
	{
		ColorRect healthIndicator = GetNode<ColorRect>("HealthBarBG/HealthBarIndicator");
		healthIndicator.Scale = new Vector2((float)currentHealth / maxHealth, 1.0f);
	}

	private void OnMaxHealthChanged(int newMaxHealth, int oldMaxHealth)
	{
		float healthRatio = (float)newMaxHealth / (float)oldMaxHealth;
		float scaleMultiplier = 1.0f + ((healthRatio - 1.0f) * HealthScaleFactor);

		Vector2 scaleTemp = this.Scale;
		scaleTemp.X += scaleMultiplier;
		this.Scale = scaleTemp;
	}
}
