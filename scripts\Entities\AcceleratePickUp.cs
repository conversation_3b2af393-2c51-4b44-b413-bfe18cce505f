using Godot;
using System;

public partial class AcceleratePickUp : Area2D
{
    [Export]
    private float MAX_ACCEL_INCREMENT = 0.5f;

	public override void _Ready()
	{
		Connect("body_entered", new Callable(this, nameof(OnBodyEntered)));
	}

	private void OnBodyEntered(Node body)
	{
		if (body is Player player)
		{
			// Method 1: Use the player parameter directly (most efficient)
			GD.Print("(B)MaxPushSpeed: " + player.PushForce);
			player.PushForce += MAX_ACCEL_INCREMENT;
			GD.Print("(A)MaxPushSpeed: " + player.PushForce);

			PlayPickupSound();
			QueueFree();
		}
	}

	private void PlayPickupSound()
	{
		var pickupAudio = GetNode<AudioStreamPlayer2D>("AudioPickup");
		if (pickupAudio?.Stream != null)
		{
			// Create a persistent audio player that survives pickup destruction
			AudioStreamPlayer2D persistentAudio = new AudioStreamPlayer2D
			{
				Stream = pickupAudio.Stream,
				GlobalPosition = GlobalPosition,
				VolumeDb = pickupAudio.VolumeDb,
				PitchScale = pickupAudio.PitchScale
			};
			
			GetParent().AddChild(persistentAudio);
			persistentAudio.Play();
			persistentAudio.Finished += () => persistentAudio.QueueFree();
		}
	}
}
