[gd_scene load_steps=5 format=3 uid="uid://d1ikyvs5g82jh"]

[ext_resource type="Script" uid="uid://dyuc8o8d5viku" path="res://scripts/Entities/HealthPickup.cs" id="1_0qiso"]
[ext_resource type="Texture2D" uid="uid://d31cawj36vptb" path="res://Assets/Entities/HealCrystal.png" id="2_pppuu"]
[ext_resource type="AudioStream" uid="uid://di18khyj8whn7" path="res://Assets/Audio/Pickup.wav" id="3_pppuu"]

[sub_resource type="CircleShape2D" id="CircleShape2D_t3exx"]
radius = 72.111

[node name="HealthPickup" type="Area2D"]
z_index = 1
collision_layer = 32
collision_mask = 32
monitorable = false
script = ExtResource("1_0qiso")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
scale = Vector2(0.25, 0.25)
shape = SubResource("CircleShape2D_t3exx")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(2, 2)
texture = ExtResource("2_pppuu")

[node name="AudioPickup" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("3_pppuu")
