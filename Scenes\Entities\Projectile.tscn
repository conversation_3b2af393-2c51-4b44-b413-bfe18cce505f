[gd_scene load_steps=5 format=3 uid="uid://dl8pwx8asqdhi"]

[ext_resource type="Script" uid="uid://b2wjaepicfg0" path="res://scripts/Entities/Projectile.cs" id="1_058gr"]
[ext_resource type="Script" uid="uid://de1k674wrdu1i" path="res://scripts/Nodes/Damageable.cs" id="2_akala"]

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_lal66"]

[sub_resource type="CircleShape2D" id="CircleShape2D_8cioo"]
radius = 8.0

[node name="Projectile" type="RigidBody2D"]
collision_layer = 0
collision_mask = 25
mass = 0.1
gravity_scale = 0.0
contact_monitor = true
max_contacts_reported = 2
script = ExtResource("1_058gr")
speed = 450.0
lifetime = 5.0
damage = 5
metadata/_edit_horizontal_guides_ = [-18.0]

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("PlaceholderTexture2D_lal66")

[node name="ColorRect" type="ColorRect" parent="."]
offset_left = -8.0
offset_top = -8.0
offset_right = 8.0
offset_bottom = 8.0
color = Color(1, 0, 0, 1)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_8cioo")

[node name="Damageable" type="Node2D" parent="."]
script = ExtResource("2_akala")
maxHealth = 1
metadata/_custom_type_script = "uid://de1k674wrdu1i"

[connection signal="body_entered" from="." to="." method="OnBodyEntered"]
