[gd_scene load_steps=4 format=3 uid="uid://cb0hh7ykc7k8h"]

[ext_resource type="Script" uid="uid://becdd4qrxtn5l" path="res://scripts/Nodes/SpawnerPoint.cs" id="1_h8hmb"]
[ext_resource type="PackedScene" uid="uid://cpsmollrixa0k" path="res://Scenes/Entities/BasicEnemy.tscn" id="2_vprqs"]
[ext_resource type="PackedScene" uid="uid://dwskf2nkyrrcw" path="res://Scenes/Entities/ShotgunEnemy.tscn" id="3_2rv3m"]

[node name="SpawnerPoint" type="Node2D"]
script = ExtResource("1_h8hmb")
EnemyType1 = ExtResource("2_vprqs")
EnemyType2 = ExtResource("3_2rv3m")
SpawnRadius = 500.0
SpawnInterval = 25.0

[node name="SpawnTimer" type="Timer" parent="."]
wait_time = 0.001
autostart = true

[connection signal="timeout" from="SpawnTimer" to="." method="_on_spawn_timer_timeout"]
