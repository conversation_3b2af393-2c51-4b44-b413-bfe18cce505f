using Godot;
using System;

public partial class GameOver : Control
{
	public override void _Ready()
	{
		ProcessMode = ProcessModeEnum.Always;
		Button Retry = GetNode<Button>("VBoxContainer/Retry");
		GetNode<Button>("VBoxContainer/Quit").Pressed += QuitPressed;
		Retry.Pressed += RetryPressed;
	}
	
	private void RetryPressed(){
		GetNode<GameManager>("/root/GameManager").Score = 0; //Reset Score
		GetTree().ChangeSceneToFile("uid://cyxsaoo1vln6a");
	}
	
	private void QuitPressed()
	{
		GetTree().Quit();
	}
}
