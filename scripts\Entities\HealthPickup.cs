using Godot;
using System;

public partial class HealthPickup : Area2D
{
	[Export]
	private int PERCENTILE_HEAL_AMOUNT = 20; // 20% of max health

	public override void _Ready()
	{
		Connect("body_entered", new Callable(this, nameof(OnBodyEntered)));
	}

	private void OnBodyEntered(Node body)
	{
		if (body is Player player)
		{
			var targetDamageable = player.GetNodeOrNull<Damageable>("Damageable");
			if (targetDamageable != null)
			{
				if (targetDamageable.Heal(PERCENTILE_HEAL_AMOUNT))
				{
					PlayPickupSound();
					QueueFree();
				}
			}
		}
	}
	private void PlayPickupSound()
	{
		var pickupAudio = GetNode<AudioStreamPlayer2D>("AudioPickup");
		if (pickupAudio?.Stream != null)
		{
			// Create a persistent audio player that survives pickup destruction
			AudioStreamPlayer2D persistentAudio = new AudioStreamPlayer2D
			{
				Stream = pickupAudio.Stream,
				GlobalPosition = GlobalPosition,
				VolumeDb = pickupAudio.VolumeDb,
				PitchScale = pickupAudio.PitchScale
			};
			
			GetParent().AddChild(persistentAudio);
			persistentAudio.Play();
			persistentAudio.Finished += () => persistentAudio.QueueFree();
		}
	}
}
