[gd_scene load_steps=5 format=3 uid="uid://spsggiksghx"]

[ext_resource type="Script" uid="uid://bjxhhntb7h6na" path="res://scripts/Entities/SpeedPickUp.cs" id="1_nsj6e"]
[ext_resource type="Texture2D" uid="uid://dcvadeskt5l8x" path="res://Assets/Entities/SpeedPickUp.png" id="2_nsj6e"]
[ext_resource type="AudioStream" uid="uid://di18khyj8whn7" path="res://Assets/Audio/Pickup.wav" id="3_gbgo0"]

[sub_resource type="CircleShape2D" id="CircleShape2D_t3exx"]
radius = 56.0

[node name="SpeedPickUp" type="Area2D"]
z_index = 1
collision_layer = 32
collision_mask = 32
monitorable = false
script = ExtResource("1_nsj6e")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
scale = Vector2(0.25, 0.25)
shape = SubResource("CircleShape2D_t3exx")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(2.5, 2.5)
texture = ExtResource("2_nsj6e")

[node name="AudioPickup" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("3_gbgo0")
