using Godot;
using System;

[GlobalClass]
public partial class SpawnerPoint : Node2D
{
	[Export]
	public PackedScene EnemyType1 { get; set; }
	[Export]
	public PackedScene EnemyType2 { get; set; }
	[Export]
	public float SpawnRadius { get; set; }
	[Export]
	public float SpawnInterval { get; set; }
	[Export]
	public bool enabled = true;
	[Export]
	private float eliteChance = 0.15f;
	private RandomNumberGenerator rng;
	private Node2D enemy;

	public override void _Ready()
	{
		rng = new RandomNumberGenerator();
		rng.Randomize();
		GetNodeOrNull<Timer>("SpawnTimer").WaitTime = SpawnInterval;
	}

	private void _on_spawn_timer_timeout()
	{
		SpawnEnemy();
	}

	public void SpawnEnemy()
	{   //This is a basic check (this is totally AI I am not that smart LoL unaliving myself rn)
		if (EnemyType1 == null || !enabled)
		{
			GD.PrintErr("Base Enemy is null! This can not be empty.");
			return;
		}
		if (EnemyType2 != null)
		{
			if (rng.Randf() <= eliteChance)
			{
				enemy = (Node2D)EnemyType2.Instantiate();
			}
			else
			{
				enemy = (Node2D)EnemyType1.Instantiate();
			}
		}

		// Set idle to false when enemy is spawned
		if (enemy.HasMethod("set") && enemy.Get("idle").VariantType != Variant.Type.Nil)
		{
			enemy.Set("idle", false);
		}

		// Calculate random position within the radius
		float angle = rng.RandfRange(0, Mathf.Tau); // Random angle (0 to 2π)

		// Convert polar coordinates to Cartesian
		Vector2 offset = new Vector2(
			Mathf.Cos(angle) * SpawnRadius,
			Mathf.Sin(angle) * SpawnRadius
		);
		// Set the enemy position relative to the spawner
		enemy.GlobalPosition = GlobalPosition + offset;
		//Set the enemy to face a random Direction (important for Basic AI implementation)
		enemy.Rotation = rng.RandfRange(0, Mathf.Tau);
		//Must make the enemy bot a child of the Spawner and added it to the root.
		GetTree().Root.GetNode("Root").AddChild(enemy);
		//once again AI kysing even hard on myself
	}

}
