[gd_scene load_steps=37 format=3 uid="uid://b34pseyhu7bx0"]

[ext_resource type="Script" uid="uid://dx3ocrfd3ogal" path="res://scripts/Entities/Boss.cs" id="1_js1vt"]
[ext_resource type="PackedScene" uid="uid://cb0hh7ykc7k8h" path="res://Scenes/Entities/spawner_point.tscn" id="2_250ht"]
[ext_resource type="Script" uid="uid://de1k674wrdu1i" path="res://scripts/Nodes/Damageable.cs" id="4_kpr8r"]
[ext_resource type="Texture2D" uid="uid://dgtls1m4amqng" path="res://Assets/Entities/Boss.png" id="5_250ht"]
[ext_resource type="AudioStream" uid="uid://i2wpdw5bg50v" path="res://Assets/Audio/Shoot.wav" id="6_pmagn"]
[ext_resource type="AudioStream" uid="uid://bh20k2hpijo4k" path="res://Assets/Audio/enemy_explode.wav" id="7_v0hci"]
[ext_resource type="Texture2D" uid="uid://b4qulhfqquaa8" path="res://Assets/Entities/V3x_Destruction_Animation_ss.png" id="9_jyhgn"]

[sub_resource type="AtlasTexture" id="AtlasTexture_cu4y6"]
atlas = ExtResource("5_250ht")
region = Rect2(0, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_o7mmd"]
atlas = ExtResource("5_250ht")
region = Rect2(640, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_8fj0k"]
atlas = ExtResource("5_250ht")
region = Rect2(1280, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_6475c"]
atlas = ExtResource("5_250ht")
region = Rect2(1920, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_pf1ho"]
atlas = ExtResource("5_250ht")
region = Rect2(2560, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_3gwcq"]
atlas = ExtResource("5_250ht")
region = Rect2(3200, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_cuo1o"]
atlas = ExtResource("5_250ht")
region = Rect2(3840, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_lbkqg"]
atlas = ExtResource("5_250ht")
region = Rect2(4480, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_cnf42"]
atlas = ExtResource("5_250ht")
region = Rect2(5120, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_0ydpm"]
atlas = ExtResource("5_250ht")
region = Rect2(5760, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_fwtca"]
atlas = ExtResource("5_250ht")
region = Rect2(6400, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_isjo6"]
atlas = ExtResource("5_250ht")
region = Rect2(7040, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_3ht26"]
atlas = ExtResource("5_250ht")
region = Rect2(7680, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_go7ql"]
atlas = ExtResource("5_250ht")
region = Rect2(8320, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_gkprf"]
atlas = ExtResource("5_250ht")
region = Rect2(8960, 0, 640, 960)

[sub_resource type="AtlasTexture" id="AtlasTexture_er41s"]
atlas = ExtResource("5_250ht")
region = Rect2(9600, 0, 640, 960)

[sub_resource type="SpriteFrames" id="SpriteFrames_o01a7"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_cu4y6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o7mmd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8fj0k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6475c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pf1ho")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3gwcq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cuo1o")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lbkqg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cnf42")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0ydpm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fwtca")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_isjo6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3ht26")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_go7ql")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gkprf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_er41s")
}],
"loop": true,
"name": &"new_animation",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_cfbw8"]
atlas = ExtResource("9_jyhgn")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ufd82"]
atlas = ExtResource("9_jyhgn")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_5ljhl"]
atlas = ExtResource("9_jyhgn")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mp3hv"]
atlas = ExtResource("9_jyhgn")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_dpxdw"]
atlas = ExtResource("9_jyhgn")
region = Rect2(160, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ga5wa"]
atlas = ExtResource("9_jyhgn")
region = Rect2(192, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_vgxrk"]
atlas = ExtResource("9_jyhgn")
region = Rect2(224, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_als3m"]
atlas = ExtResource("9_jyhgn")
region = Rect2(256, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_e58ik"]
atlas = ExtResource("9_jyhgn")
region = Rect2(288, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mp2ot"]
atlas = ExtResource("9_jyhgn")
region = Rect2(320, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_k0b5j"]
atlas = ExtResource("9_jyhgn")
region = Rect2(352, 0, 32, 32)

[sub_resource type="SpriteFrames" id="SpriteFrames_oxylo"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_cfbw8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ufd82")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5ljhl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mp3hv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dpxdw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ga5wa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vgxrk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_als3m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_e58ik")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mp2ot")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_k0b5j")
}],
"loop": false,
"name": &"EnemyDeath",
"speed": 30.0
}]

[node name="Boss" type="CharacterBody2D"]
rotation = 0.0165692
scale = Vector2(2.5, 2.5)
collision_layer = 2
collision_mask = 0
script = ExtResource("1_js1vt")

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="."]
position = Vector2(-24.7263, 101.224)
scale = Vector2(1.53416, 0.76981)
polygon = PackedVector2Array(-19.235, 7.391, -28.832, -17.756, -7.767, -118.75, -17.796, -195.851, 2.125, -277.579, 30.289, -277.47, 50.275, -194.461, 40.491, -116.706, 62.967, -16.63, 51.43, 7.136)
disabled = true

[node name="SpawnerPoint" parent="." instance=ExtResource("2_250ht")]
eliteChance = 0.25

[node name="Damageable" type="Node2D" parent="."]
position = Vector2(-23.9304, 4.39709)
script = ExtResource("4_kpr8r")
metadata/_custom_type_script = "uid://de1k674wrdu1i"

[node name="AudioShoot" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("6_pmagn")
volume_db = -8.71
pitch_scale = 1.5

[node name="AudioExplode" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("7_v0hci")
pitch_scale = 0.84

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
scale = Vector2(0.25, 0.25)
sprite_frames = SubResource("SpriteFrames_o01a7")
animation = &"new_animation"
autoplay = "new_animation"

[node name="DeathSprite" type="AnimatedSprite2D" parent="."]
visible = false
sprite_frames = SubResource("SpriteFrames_oxylo")
animation = &"EnemyDeath"

[connection signal="animation_finished" from="DeathSprite" to="." method="_on_death_sprite_animation_finished"]
