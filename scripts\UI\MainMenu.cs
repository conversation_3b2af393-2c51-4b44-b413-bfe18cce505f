using Godot;
using System;

public partial class MainMenu : Control
{   
	private AudioStreamPlayer Hover;
	private AudioStreamPlayer Click;

	public override void _Ready()
	{
		ProcessMode = ProcessModeEnum.Always;
		Button startbutton = GetNode<Button>("VBoxContainer/StartButton");
		GetNode<Button>("VBoxContainer/OptionButton").Pressed += OptionPressed;
		GetNode<Button>("VBoxContainer/QuitButton").Pressed += QuitPressed;
		startbutton.GrabFocus();
		startbutton.Pressed += StartPressed;
	}

	private void StartPressed()
	{

		if (GameManager.IsPaused)
		{
			GetNode<GameManager>("/root/GameManager").TogglePauseMenu();
		}
		else
		{
			GetTree().ChangeSceneToFile("uid://cyxsaoo1vln6a");
		}
	}
	
	private void OptionPressed()
	{
		GD.Print("Option Button Pressed");
	}

	private void QuitPressed()
	{
		GetTree().Quit();
	}
}
