[gd_scene load_steps=9 format=3 uid="uid://75gcjy16ywpy"]

[ext_resource type="Script" uid="uid://5i0plxmxpb0y" path="res://scripts/Entities/Player.cs" id="1_ushfy"]
[ext_resource type="Texture2D" uid="uid://cvuuttq84fdvs" path="res://Assets/Entities/Player.png" id="2_kyqiw"]
[ext_resource type="Script" uid="uid://de1k674wrdu1i" path="res://scripts/Nodes/Damageable.cs" id="3_gntrk"]
[ext_resource type="Script" uid="uid://dgshvmje4dtpo" path="res://scripts/Nodes/MovableObject.cs" id="4_g4oe6"]
[ext_resource type="Script" uid="uid://c45n6hydrgtv2" path="res://scripts/Engine/PlayerCamera.cs" id="4_td8ue"]
[ext_resource type="PackedScene" uid="uid://bwn7rvv115i4o" path="res://Scenes/UI/health_bar.tscn" id="4_yu0ev"]
[ext_resource type="PackedScene" uid="uid://bgcup64f7nmi" path="res://Scenes/UI/score.tscn" id="6_ynt0t"]

[sub_resource type="CircleShape2D" id="CircleShape2D_gntrk"]
radius = 16.0

[node name="Player" type="Node"]
physics_interpolation_mode = 1

[node name="PlayerBody" type="CharacterBody2D" parent="."]
physics_interpolation_mode = 1
scale = Vector2(2.5, 2.5)
collision_layer = 33
collision_mask = 58
script = ExtResource("1_ushfy")
Damage = 5
HealthScaleFactor = 0.25

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerBody"]
shape = SubResource("CircleShape2D_gntrk")

[node name="AsteroidTemp" type="Sprite2D" parent="PlayerBody"]
texture = ExtResource("2_kyqiw")

[node name="Damageable" type="Node2D" parent="PlayerBody"]
script = ExtResource("3_gntrk")
isHealable = true
metadata/_custom_type_script = "uid://de1k674wrdu1i"

[node name="MovableObject" type="Node2D" parent="PlayerBody"]
script = ExtResource("4_g4oe6")
metadata/_custom_type_script = "uid://dgshvmje4dtpo"

[node name="PlayerCamera" type="Camera2D" parent="."]
script = ExtResource("4_td8ue")

[node name="Score" parent="." instance=ExtResource("6_ynt0t")]
layout_mode = 3
anchors_preset = 0
offset_left = 578.0
offset_top = -511.0
offset_right = 693.0
offset_bottom = -488.0
scale = Vector2(3, 3)

[node name="HealthBar" parent="." instance=ExtResource("4_yu0ev")]
offset_left = -921.0
offset_top = -498.0
offset_right = -819.0
offset_bottom = -491.0
scale = Vector2(5, 5)
IsPlayerHealthBar = true
