using Godot;
using System;

public partial class Projectile : RigidBody2D
{
	[Export]
	private float speed = 300.0f;
	[Export]
	private float lifetime = 2.0f;
	[Export]
	private float timer = 0.0f;
	[Export]
	private int damage = 25;
	private Damageable damageable;

	public override void _Ready()
	{
		damageable = GetNode<Damageable>("Damageable");
		damageable.Death += _OnDamageableDeath;
	}

	public void fireBullet(float rotation)
	{
		// Calculate direction based on rotation
		Vector2 direction = new Vector2(Mathf.Sin(rotation), -Mathf.Cos(rotation));
		// Apply impulse in the direction of travel
		ApplyImpulse(direction * speed);
	}

	public override void _Process(double delta)
	{
		timer += (float)delta;

		// Destroy projectile after lifetime
		if (timer >= lifetime)
		{
			QueueFree();
		}
	}

	private void OnBodyEntered(Node body)
	{
		if (body is not Projectile && body is not Missile)
		{
			if (body is Node2D)
			{
				var targetDamageable = body.GetNodeOrNull<Damageable>("Damageable");
				if (targetDamageable != null)
				{
					targetDamageable.TakeDamage(damage);
				}
			}
			QueueFree(); // Destroy projectile after hitting something
		}
	}
	
	public void _OnDamageableDeath()
	{
		QueueFree();
	}
}
