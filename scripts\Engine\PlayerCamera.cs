using Godot;
using System;
using System.Runtime.CompilerServices;

public partial class PlayerCamera : Camera2D
{
    [Export]
    private bool UseSmoothingFunctionality = true;
    [Export]
    private float UIOffset = 100f; // UI Element Offset From Side of Screen - Breached into with Smoothing
    [Export]
    private float UIStrictOffset = 10f; // UI Element Offset From Side of Screen - Will never smooth past this point
    [Export]
    private float MaxSmoothening = 250f; // Maximum pixels allowed to be lagging behind asteroid
    private CharacterBody2D Player;
    private HealthBar HealthBarNode;
    private Score ScoreNode;

    public override void _Ready()
    {
        this.PositionSmoothingEnabled = UseSmoothingFunctionality;
    }


    public override void _Process(double delta)
    {
        if (Player == null)
        {
            Player = GetNode<GameManager>("/root/GameManager").GetPlayerNode().GetNode<CharacterBody2D>("PlayerBody");
        }

        if (HealthBarNode == null)
        {
            HealthBarNode = GetNode<GameManager>("/root/GameManager").GetPlayerNode().GetNode<HealthBar>("HealthBar");
            HealthBarNode.Visible = true;
        }

        if (ScoreNode == null)
        {
            ScoreNode = GetNode<GameManager>("/root/GameManager").GetPlayerNode().GetNode<Score>("Score");
            ScoreNode.Visible = true;
        }

        if (Player != null)
        {
            HandlePlayerCalculations(delta);
        }

        if (HealthBarNode != null || ScoreNode != null)
        {
            HandleUICalculations(delta);
        }
    }

    private void HandlePlayerCalculations(double delta)
    {
        GlobalPosition = Player.GlobalPosition.Round();
    }

    private void HandleUICalculations(double delta)
    {
        Vector2 viewportSize = this.GetViewportRect().Size;
        Vector2 screenCenter = this.GetScreenCenterPosition().Round();
        Vector2 zoomFactor = this.Zoom;
		float aspectRatioFactor = viewportSize.X / viewportSize.Y;

        Vector2 lagValue;
        if (!this.PositionSmoothingEnabled)
        {
            lagValue = Vector2.Zero;
            screenCenter = Player.GlobalPosition.Round();
        }
        else
        {
            Vector2 offset = Player.GlobalPosition - screenCenter;
            offset.Y /= aspectRatioFactor;
            if (offset.X > MaxSmoothening)
            {
                offset.X = MaxSmoothening;
            }
            else if (offset.X < -MaxSmoothening)
            {
                offset.X = -MaxSmoothening;
            }

            if (offset.Y > MaxSmoothening / aspectRatioFactor)
            {
                offset.Y = MaxSmoothening / aspectRatioFactor;
            }
            else if (offset.Y < -MaxSmoothening / aspectRatioFactor)
            {
                offset.Y = -MaxSmoothening / aspectRatioFactor;
            }

            offset = offset.Round();


            lagValue.X = offset.X / MaxSmoothening * UIOffset;
            lagValue.Y = offset.Y / (MaxSmoothening / aspectRatioFactor) * (UIOffset / aspectRatioFactor);
        }

        if (HealthBarNode != null)
            {
                Vector2 topLeft = screenCenter - (viewportSize / zoomFactor / 2);

                Vector2 healthBarPos;
                healthBarPos.X = topLeft.X + UIStrictOffset + UIOffset + lagValue.X;
                healthBarPos.Y = topLeft.Y + (UIStrictOffset / aspectRatioFactor) + (UIOffset / aspectRatioFactor) + lagValue.Y;

                healthBarPos = healthBarPos.Round();

                HealthBarNode.GlobalPosition = healthBarPos;
            }

        if (ScoreNode != null)
        {
            Vector2 topRight;
		    topRight.X = screenCenter.X + (viewportSize.X / zoomFactor.X / 2);
            topRight.Y = screenCenter.Y - (viewportSize.Y / zoomFactor.Y / 2);

            Vector2 scoreSize;
            scoreSize.X = ScoreNode.Size.X * ScoreNode.Scale.X;
            scoreSize.Y = 4f * ScoreNode.Scale.Y;

            Vector2 scorePos;
            scorePos.X = topRight.X - UIStrictOffset - UIOffset - scoreSize.X + lagValue.X;
            scorePos.Y = topRight.Y + (UIStrictOffset / aspectRatioFactor) + (UIOffset / aspectRatioFactor) - scoreSize.Y + lagValue.Y;

            scorePos = scorePos.Round();

            ScoreNode.GlobalPosition = scorePos;
        }
    }
}
