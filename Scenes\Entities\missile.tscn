[gd_scene load_steps=7 format=3 uid="uid://lbhg5q47m8o8"]

[ext_resource type="Script" uid="uid://ds3sskoucnsfx" path="res://scripts/Entities/Missile.cs" id="1_fnlsn"]
[ext_resource type="Script" uid="uid://de1k674wrdu1i" path="res://scripts/Nodes/Damageable.cs" id="2_1w0dl"]
[ext_resource type="Texture2D" uid="uid://bmqfkw2r4r00b" path="res://Assets/Entities/Missile.png" id="2_2r2eg"]
[ext_resource type="Texture2D" uid="uid://dy2pgbbnw5tfp" path="res://Assets/Entities/Explosion.png" id="2_06xxk"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_fnlsn"]

[sub_resource type="CircleShape2D" id="CircleShape2D_fnlsn"]
radius = 88.5664

[node name="Missile" type="RigidBody2D"]
rotation = 0.0153091
collision_layer = 0
collision_mask = 25
mass = 0.1
gravity_scale = 0.0
contact_monitor = true
max_contacts_reported = 2
script = ExtResource("1_fnlsn")
speed = 10.0
lifetime = 15.0
damage = 5
turnSpeed = 0.35
metadata/_edit_horizontal_guides_ = [-30.0]

[node name="Missile" type="Sprite2D" parent="."]
position = Vector2(-2.98023e-07, 1.19209e-07)
scale = Vector2(2, 2)
texture = ExtResource("2_2r2eg")

[node name="MissileHitBox" type="CollisionShape2D" parent="."]
position = Vector2(-2.79397e-09, 5.96046e-08)
scale = Vector2(0.610098, 2.05562)
shape = SubResource("CapsuleShape2D_fnlsn")

[node name="Explosion" type="Sprite2D" parent="."]
visible = false
position = Vector2(-2.04569, -2.96903)
scale = Vector2(5.3731, 5.3731)
texture = ExtResource("2_06xxk")

[node name="ExplosionArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 15
monitoring = false

[node name="ExplosionRadius" type="CollisionShape2D" parent="ExplosionArea"]
position = Vector2(0, -3.72529e-09)
shape = SubResource("CircleShape2D_fnlsn")

[node name="Damageable" type="Node2D" parent="."]
scale = Vector2(0.873994, 0.983187)
script = ExtResource("2_1w0dl")
maxHealth = 1
metadata/_custom_type_script = "uid://de1k674wrdu1i"

[connection signal="body_entered" from="." to="." method="OnBodyEntered"]
[connection signal="body_entered" from="ExplosionArea" to="." method="OnExplosionBodyEntered"]
