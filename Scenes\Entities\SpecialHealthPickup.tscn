[gd_scene load_steps=5 format=3 uid="uid://bb3yxnyf1678y"]

[ext_resource type="Script" uid="uid://iu6wl7rcxqb1" path="res://scripts/Entities/SpecialHealthPickup.cs" id="1_0qiso"]
[ext_resource type="Texture2D" uid="uid://b8vvka86amnvn" path="res://Assets/Entities/GrowCrystal.png" id="2_1vefx"]
[ext_resource type="AudioStream" uid="uid://di18khyj8whn7" path="res://Assets/Audio/Pickup.wav" id="3_1vefx"]

[sub_resource type="CircleShape2D" id="CircleShape2D_t3exx"]
radius = 56.0

[node name="SpecialHealthPickup" type="Area2D"]
z_index = 1
collision_layer = 32
collision_mask = 32
monitorable = false
script = ExtResource("1_0qiso")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
scale = Vector2(0.25, 0.25)
shape = SubResource("CircleShape2D_t3exx")

[node name="Sprite2D" type="Sprite2D" parent="."]
position = Vector2(-2, 2)
scale = Vector2(2, 2)
texture = ExtResource("2_1vefx")

[node name="AudioPickup" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("3_1vefx")
