[gd_scene load_steps=32 format=3 uid="uid://cpsmollrixa0k"]

[ext_resource type="Script" uid="uid://bk74as1jm7rvd" path="res://scripts/Entities/BasicEnemy.cs" id="1_wovta"]
[ext_resource type="PackedScene" uid="uid://dl8pwx8asqdhi" path="res://Scenes/Entities/Projectile.tscn" id="2_hp7c0"]
[ext_resource type="PackedScene" uid="uid://xu0japeolf40" path="res://Scenes/Entities/enemy_Debris.tscn" id="3_445h8"]
[ext_resource type="PackedScene" uid="uid://lbhg5q47m8o8" path="res://Scenes/Entities/missile.tscn" id="3_v0hci"]
[ext_resource type="Script" uid="uid://de1k674wrdu1i" path="res://scripts/Nodes/Damageable.cs" id="4_kpr8r"]
[ext_resource type="AudioStream" uid="uid://i2wpdw5bg50v" path="res://Assets/Audio/Shoot.wav" id="6_pmagn"]
[ext_resource type="AudioStream" uid="uid://bh20k2hpijo4k" path="res://Assets/Audio/enemy_explode.wav" id="7_v0hci"]
[ext_resource type="Texture2D" uid="uid://ol4jhh1bve4v" path="res://Assets/Entities/V3x_Animation_ss.png" id="8_jyhgn"]
[ext_resource type="Texture2D" uid="uid://b4qulhfqquaa8" path="res://Assets/Entities/V3x_Destruction_Animation_ss.png" id="9_jyhgn"]

[sub_resource type="CircleShape2D" id="CircleShape2D_wovta"]
radius = 16.1245

[sub_resource type="AtlasTexture" id="AtlasTexture_xiyif"]
atlas = ExtResource("8_jyhgn")
region = Rect2(0, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_j6l2x"]
atlas = ExtResource("8_jyhgn")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_oxylo"]
atlas = ExtResource("8_jyhgn")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_o8inh"]
atlas = ExtResource("8_jyhgn")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_3q8rv"]
atlas = ExtResource("8_jyhgn")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_8p5wn"]
atlas = ExtResource("8_jyhgn")
region = Rect2(160, 0, 32, 32)

[sub_resource type="SpriteFrames" id="SpriteFrames_o01a7"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_xiyif")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j6l2x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_oxylo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o8inh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3q8rv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8p5wn")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_cfbw8"]
atlas = ExtResource("9_jyhgn")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ufd82"]
atlas = ExtResource("9_jyhgn")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_5ljhl"]
atlas = ExtResource("9_jyhgn")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mp3hv"]
atlas = ExtResource("9_jyhgn")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_dpxdw"]
atlas = ExtResource("9_jyhgn")
region = Rect2(160, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ga5wa"]
atlas = ExtResource("9_jyhgn")
region = Rect2(192, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_vgxrk"]
atlas = ExtResource("9_jyhgn")
region = Rect2(224, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_als3m"]
atlas = ExtResource("9_jyhgn")
region = Rect2(256, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_e58ik"]
atlas = ExtResource("9_jyhgn")
region = Rect2(288, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mp2ot"]
atlas = ExtResource("9_jyhgn")
region = Rect2(320, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_k0b5j"]
atlas = ExtResource("9_jyhgn")
region = Rect2(352, 0, 32, 32)

[sub_resource type="SpriteFrames" id="SpriteFrames_oxylo"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_cfbw8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ufd82")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5ljhl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mp3hv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dpxdw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ga5wa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vgxrk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_als3m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_e58ik")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mp2ot")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_k0b5j")
}],
"loop": false,
"name": &"EnemyDeath",
"speed": 30.0
}]

[sub_resource type="CircleShape2D" id="CircleShape2D_jyhgn"]
radius = 258.425

[sub_resource type="CircleShape2D" id="CircleShape2D_cfbw8"]
radius = 26.4499

[node name="BasicEnemy" type="CharacterBody2D"]
rotation = 0.0165692
scale = Vector2(2.5, 2.5)
collision_layer = 2
collision_mask = 11
script = ExtResource("1_wovta")
missilechance = 0.05
ProjectileScene = ExtResource("2_hp7c0")
MissileScene = ExtResource("3_v0hci")
DebrisScene = ExtResource("3_445h8")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_wovta")

[node name="Damageable" type="Node2D" parent="."]
position = Vector2(-23.9304, 4.39709)
script = ExtResource("4_kpr8r")
maxHealth = 5
metadata/_custom_type_script = "uid://de1k674wrdu1i"

[node name="AudioShoot" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("6_pmagn")
volume_db = -8.71
pitch_scale = 1.5

[node name="AudioExplode" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("7_v0hci")
pitch_scale = 0.84

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = SubResource("SpriteFrames_o01a7")
autoplay = "default"

[node name="DeathSprite" type="AnimatedSprite2D" parent="."]
visible = false
sprite_frames = SubResource("SpriteFrames_oxylo")
animation = &"EnemyDeath"

[node name="Range" type="Area2D" parent="."]
collision_layer = 0

[node name="CollisionShape2D" type="CollisionShape2D" parent="Range"]
shape = SubResource("CircleShape2D_jyhgn")

[node name="Bumper" type="Area2D" parent="."]
position = Vector2(-0.298232, -17.9975)
scale = Vector2(0.816741, 1.48735)
collision_layer = 0
collision_mask = 10

[node name="CollisionShape2D" type="CollisionShape2D" parent="Bumper"]
position = Vector2(-0.692544, -6.71801)
shape = SubResource("CircleShape2D_cfbw8")

[node name="Check" type="Timer" parent="Bumper"]
autostart = true

[connection signal="animation_finished" from="DeathSprite" to="." method="_on_death_sprite_animation_finished"]
[connection signal="body_entered" from="Range" to="." method="InRange"]
[connection signal="body_exited" from="Range" to="." method="OutRange"]
[connection signal="body_entered" from="Bumper" to="." method="Avoiding"]
[connection signal="body_exited" from="Bumper" to="." method="NotAvoiding"]
[connection signal="timeout" from="Bumper/Check" to="." method="_on_check_timeout"]
