[gd_scene load_steps=5 format=3 uid="uid://xu0japeolf40"]

[ext_resource type="Script" uid="uid://crtpthkvbit8o" path="res://scripts/Entities/SpaceDebris.cs" id="1_k8x6j"]
[ext_resource type="Texture2D" uid="uid://de2uxo720crt0" path="res://Assets/Entities/Ship_Damaged.png" id="1_txmfh"]
[ext_resource type="Script" uid="uid://dgshvmje4dtpo" path="res://scripts/Nodes/MovableObject.cs" id="3_qhhtn"]

[sub_resource type="CircleShape2D" id="CircleShape2D_txmfh"]
radius = 33.0

[node name="EnemyDebris" type="RigidBody2D"]
collision_layer = 8
collision_mask = 8
contact_monitor = true
max_contacts_reported = 3
script = ExtResource("1_k8x6j")
debrisTexture1 = ExtResource("1_txmfh")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(-3, 0)
shape = SubResource("CircleShape2D_txmfh")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(2.3125, 2.3125)

[node name="MovableObject" type="Node2D" parent="."]
script = ExtResource("3_qhhtn")
metadata/_custom_type_script = "uid://dgshvmje4dtpo"
