using Godot;
using System;

[GlobalClass]
public partial class MovableObject : Node2D
{
    [Export]
    private bool isMovableObject = true;

    public void Pushed(int force, Vector2 ExplosionLocation)
    {
        var body = GetParent() as Node2D;
        Vector2 pushDirection = body.GlobalPosition - ExplosionLocation;
        pushDirection = pushDirection.Normalized();
        if (body is RigidBody2D rigidBody)
        {
            rigidBody.ApplyImpulse(pushDirection * force * 5); //This adds spin to the object when pushed
            var myCurrentVelocity = pushDirection * force/2; // doing a straight applyimpulse only rotates as above
            rigidBody.LinearVelocity = myCurrentVelocity;
            
        }
        else if (body is CharacterBody2D characterBody2D)
        {
            characterBody2D.Velocity += pushDirection * force;
        }
    }
    public void SetDamageable(bool isMovableObject)
    {
        this.isMovableObject = isMovableObject;
    }
    public bool GetMovableObject()
    {
        return isMovableObject;
    }
}



