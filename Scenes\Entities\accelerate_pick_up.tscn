[gd_scene load_steps=5 format=3 uid="uid://ckg3p3078vvwj"]

[ext_resource type="Script" uid="uid://qcsepfinm8qi" path="res://scripts/Entities/AcceleratePickUp.cs" id="1_nsfa4"]
[ext_resource type="Texture2D" uid="uid://cfbor32bi5fio" path="res://Assets/Entities/Planet5.png" id="2_ma05n"]
[ext_resource type="AudioStream" uid="uid://di18khyj8whn7" path="res://Assets/Audio/Pickup.wav" id="3_lcmvp"]

[sub_resource type="CircleShape2D" id="CircleShape2D_t3exx"]
radius = 56.0

[node name="AcceleratePickUp" type="Area2D"]
z_index = 1
collision_layer = 32
collision_mask = 32
monitorable = false
script = ExtResource("1_nsfa4")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
scale = Vector2(0.25, 0.25)
shape = SubResource("CircleShape2D_t3exx")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(0.188154, 0.188154)
texture = ExtResource("2_ma05n")

[node name="AudioPickup" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("3_lcmvp")
