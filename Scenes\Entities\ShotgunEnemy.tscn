[gd_scene load_steps=27 format=3 uid="uid://dwskf2nkyrrcw"]

[ext_resource type="Script" uid="uid://k57tdc5brok7" path="res://scripts/Entities/ShotgunEnemy.cs" id="1_rm6mr"]
[ext_resource type="PackedScene" uid="uid://dl8pwx8asqdhi" path="res://Scenes/Entities/Projectile.tscn" id="2_uix18"]
[ext_resource type="PackedScene" uid="uid://bmbgjnvqkamyq" path="res://Scenes/Entities/shotgun_debris.tscn" id="3_oxjrw"]
[ext_resource type="Texture2D" uid="uid://bnytvmraxs4pw" path="res://Assets/Entities/Shotgun.png" id="4_fmtsy"]
[ext_resource type="Texture2D" uid="uid://ijdya73lxk28" path="res://Assets/Entities/ShotgunDeathAnimation.png" id="4_uix18"]
[ext_resource type="Script" uid="uid://de1k674wrdu1i" path="res://scripts/Nodes/Damageable.cs" id="6_3k3kv"]
[ext_resource type="AudioStream" uid="uid://i2wpdw5bg50v" path="res://Assets/Audio/Shoot.wav" id="7_lgj02"]
[ext_resource type="AudioStream" uid="uid://bh20k2hpijo4k" path="res://Assets/Audio/enemy_explode.wav" id="8_fskil"]

[sub_resource type="CircleShape2D" id="CircleShape2D_wovta"]
radius = 16.1245

[sub_resource type="AtlasTexture" id="AtlasTexture_leqcl"]
atlas = ExtResource("4_uix18")
region = Rect2(0, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_efq4r"]
atlas = ExtResource("4_uix18")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ubs0d"]
atlas = ExtResource("4_uix18")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_3k3kv"]
atlas = ExtResource("4_uix18")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_lgj02"]
atlas = ExtResource("4_uix18")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_fskil"]
atlas = ExtResource("4_uix18")
region = Rect2(160, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_er1vi"]
atlas = ExtResource("4_uix18")
region = Rect2(192, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_a2nan"]
atlas = ExtResource("4_uix18")
region = Rect2(224, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_p5t08"]
atlas = ExtResource("4_uix18")
region = Rect2(256, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_5jgpq"]
atlas = ExtResource("4_uix18")
region = Rect2(288, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_doj8s"]
atlas = ExtResource("4_uix18")
region = Rect2(320, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_d6d6h"]
atlas = ExtResource("4_uix18")
region = Rect2(352, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_1x570"]
atlas = ExtResource("4_uix18")
region = Rect2(384, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_8uqwy"]
atlas = ExtResource("4_uix18")
region = Rect2(416, 0, 32, 32)

[sub_resource type="SpriteFrames" id="SpriteFrames_oxjrw"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_leqcl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_efq4r")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ubs0d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3k3kv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lgj02")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fskil")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_er1vi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a2nan")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p5t08")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5jgpq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_doj8s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_d6d6h")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1x570")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8uqwy")
}],
"loop": false,
"name": &"EnemyDeath",
"speed": 30.0
}]

[sub_resource type="CircleShape2D" id="CircleShape2D_oxjrw"]
radius = 258.425

[sub_resource type="CircleShape2D" id="CircleShape2D_uix18"]
radius = 27.7637

[node name="ShotgunEnemy" type="CharacterBody2D"]
rotation = 0.0165692
scale = Vector2(2.5, 2.5)
collision_layer = 2
collision_mask = 10
script = ExtResource("1_rm6mr")
missilechance = 0.0
ProjectileScene = ExtResource("2_uix18")
DebrisScene = ExtResource("3_oxjrw")
damage = 5
shootCooldown = 1.0
pointReward = 250

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_wovta")

[node name="Damageable" type="Node2D" parent="."]
position = Vector2(-23.9304, 4.39709)
script = ExtResource("6_3k3kv")
maxHealth = 15
metadata/_custom_type_script = "uid://de1k674wrdu1i"

[node name="AudioShoot" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("7_lgj02")
volume_db = -8.71
pitch_scale = 1.5

[node name="AudioExplode" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("8_fskil")
pitch_scale = 0.84

[node name="Sprite2D" type="Sprite2D" parent="."]
position = Vector2(-0.0132548, -0.79989)
texture = ExtResource("4_fmtsy")

[node name="DeathSprite" type="AnimatedSprite2D" parent="."]
visible = false
position = Vector2(-0.0198821, -1.19984)
sprite_frames = SubResource("SpriteFrames_oxjrw")
animation = &"EnemyDeath"

[node name="Range" type="Area2D" parent="."]
collision_layer = 0
monitorable = false

[node name="CollisionShape2D" type="CollisionShape2D" parent="Range"]
shape = SubResource("CircleShape2D_oxjrw")

[node name="Bumper" type="Area2D" parent="."]
position = Vector2(0.607696, -11.6117)
scale = Vector2(0.807235, 1.2633)
collision_layer = 0
collision_mask = 10

[node name="CollisionShape2D" type="CollisionShape2D" parent="Bumper"]
position = Vector2(-1.27825, -11.07)
shape = SubResource("CircleShape2D_uix18")

[node name="Check" type="Timer" parent="Bumper"]
autostart = true

[connection signal="animation_finished" from="DeathSprite" to="." method="_on_death_sprite_animation_finished"]
[connection signal="body_entered" from="Range" to="." method="InRange"]
[connection signal="body_exited" from="Range" to="." method="OutRange"]
[connection signal="body_entered" from="Bumper" to="." method="Avoiding"]
[connection signal="body_exited" from="Bumper" to="." method="NotAvoiding"]
[connection signal="timeout" from="Bumper/Check" to="." method="_on_check_timeout"]
