using Godot;
using System;

public partial class Boss : CharacterBody2D
{
	// Constants
	private const float BOUNCE_DAMPING = 0.85f;
	private const float DEBRIS_IMPULSE_MULTIPLIER = 0.25f;
	private const float PROJECTILE_SPAWN_OFFSET = 50.0f;

	[Export]
	private float missilechance = 0.25f; // Adjust this to control how often the enemy shoots missiles

	[Export]
	public PackedScene ProjectileScene { get; set; }

	[Export]
	public PackedScene MissileScene { get; set; }

	[Export]
	public PackedScene DebrisScene { get; set; }

	private PackedScene HealthPickupScene { get; set; } = GD.Load<PackedScene>("uid://d1ikyvs5g82jh");
	private PackedScene SpecialHealthPickupScene { get; set; } = GD.Load<PackedScene>("uid://bb3yxnyf1678y");

	[Export]
	private int damage = 25;

	[Export]
	private float shootCooldown = 2.0f; // Time between shots in seconds

	[Export]
	private float healthPickupChance = 50.0f; // Chance to drop a health pickup (0-100)

	[Export]
	private float specialHealthPickupChance = 10.0f; // Chance for the health pickup to be special (0-100)

	[Export]
	private int pointReward = 10000;

	private CharacterBody2D player;
	private AudioStreamPlayer2D shootAudio;
	private RandomNumberGenerator rng;
	private float shootTimer = 0.0f;
	private Damageable damageable;
	private AnimatedSprite2D deathAnimation;

	public override void _Ready()
	{
		damageable = GetNode<Damageable>("Damageable");
		shootAudio = GetNode<AudioStreamPlayer2D>("AudioShoot");
		rng = new RandomNumberGenerator();
		rng.Randomize();
		damageable.Death += _OnDamageableDeath;
		deathAnimation = GetNode<AnimatedSprite2D>("DeathSprite");
	}

	public override void _PhysicsProcess(double delta)
	{
		if (player == null)
		{
			var pTemp = GetNode<GameManager>("/root/GameManager").GetPlayerNode();
			if (pTemp != null)
			{
				player = pTemp.GetNode<CharacterBody2D>("PlayerBody");
			}
		}
	}

	public override void _Process(double delta)
	{
		if (player != null)
		{
			// Shooting timer
			shootTimer += (float)delta;
			if (shootTimer >= shootCooldown)
			{
				Shoot();
				shootTimer = 0.0f;
			}
		}
	}

	private void Shoot()
	{
		if (rng.Randf() < missilechance)
		{
			if (MissileScene != null)
			{
				// Create missile instance
				Missile myMissile = (Missile)MissileScene.Instantiate();

				if (myMissile is Missile missileScript)
				{
					int sidespawn = 90;
					if (rng.Randf() < 0.5f)
					{
						sidespawn = -90;
					}
					// Updated spawn offset calculation to match the new rotation
					Vector2 spawnOffset = Vector2.Up.Rotated(Rotation - sidespawn) * (PROJECTILE_SPAWN_OFFSET + 50);
					missileScript.GlobalPosition = GlobalPosition + spawnOffset;

					//missileScript.Rotation = Rotation;
					GetParent().AddChild(missileScript);
					missileScript.fireMissile(Rotation);

					// Play shoot sound
					//MissileAudio?.Play();
				}
			}
		}
		else if (ProjectileScene != null)
		{
			// Create projectile instance
			Projectile projectile = (Projectile)ProjectileScene.Instantiate();

			if (projectile is Projectile projectileScript)
			{
				// Updated spawn offset calculation to match the new rotation
				Vector2 spawnOffset = Vector2.Up.Rotated(Rotation) * PROJECTILE_SPAWN_OFFSET;
				projectileScript.GlobalPosition = GlobalPosition + spawnOffset;

				projectileScript.Rotation = Rotation;
				GetParent().AddChild(projectileScript);
				projectileScript.fireBullet(Rotation);

				// Play shoot sound
				shootAudio?.Play();
			}
		}
	}

	private void PlayExplosionSound()
	{
		var explosionAudio = GetNode<AudioStreamPlayer2D>("AudioExplode");
		if (explosionAudio?.Stream != null)
		{
			// Create a persistent audio player that survives enemy destruction
			AudioStreamPlayer2D persistentAudio = new AudioStreamPlayer2D
			{
				Stream = explosionAudio.Stream,
				GlobalPosition = GlobalPosition,
				VolumeDb = explosionAudio.VolumeDb,
				PitchScale = explosionAudio.PitchScale
			};

			GetParent().AddChild(persistentAudio);
			persistentAudio.Play();
			persistentAudio.Finished += () => persistentAudio.QueueFree();
		}
	}

	public void _OnDamageableDeath()
	{
		SetProcess(false);
		// Start death animation
		deathAnimation.Visible = true;
		deathAnimation.Play();

		// Play explosion sound
		PlayExplosionSound();

		// Increase score
		GetNode<GameManager>("/root/GameManager").Score += pointReward; // Adjust point value as needed
	}

	public void _on_death_sprite_animation_finished()
	{
		Random rand = new Random();
		if (rand.Next(0, 100) < healthPickupChance)
		{
			bool isSpecial = rand.Next(0, 100) < specialHealthPickupChance;

			Area2D healthPickup;
			if (isSpecial)
			{
				healthPickup = (SpecialHealthPickup)SpecialHealthPickupScene.Instantiate();
			}
			else
			{
				healthPickup = (HealthPickup)HealthPickupScene.Instantiate();
			}

			healthPickup.GlobalPosition = GlobalPosition;
			Callable.From(() => GetParent().AddChild(healthPickup)).CallDeferred();
		}
		// Create space debris and clean up
		Callable.From(() =>
		{
			SpaceDebris deadSprite = (SpaceDebris)DebrisScene.Instantiate();
			Vector2 spawnOffset = Vector2.Up.Rotated(Rotation);

			deadSprite.GlobalPosition = GlobalPosition + spawnOffset;
			deadSprite.ApplyImpulse(Vector2.Up.Rotated(Rotation) * Velocity.Length());
			deadSprite.Rotation = Rotation;

			GetParent().AddChild(deadSprite);
			QueueFree();
		}).CallDeferred();
	}
}	
	

