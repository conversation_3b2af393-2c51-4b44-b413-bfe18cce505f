[gd_scene load_steps=9 format=3 uid="uid://d4r4r3kx86o6"]

[ext_resource type="Texture2D" uid="uid://bvskneg5gio3" path="res://Assets/Levels/MapTemp.png" id="1_jxkuk"]
[ext_resource type="Script" uid="uid://dxv1vth4ij3i7" path="res://scripts/UI/GameOver.cs" id="1_wpaxx"]
[ext_resource type="PackedScene" uid="uid://bgcup64f7nmi" path="res://Scenes/UI/score.tscn" id="3_jqelu"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_wpaxx"]
texture = ExtResource("1_jxkuk")

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jqelu"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_w88jv"]
bg_color = Color(0.481302, 0.624292, 0.700334, 1)
corner_radius_top_left = 32
corner_radius_top_right = 32
corner_radius_bottom_right = 32
corner_radius_bottom_left = 32

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ckxb7"]
bg_color = Color(0.481302, 0.624292, 0.700334, 1)
corner_radius_top_left = 32
corner_radius_top_right = 32
corner_radius_bottom_right = 32
corner_radius_bottom_left = 32

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_73uea"]
bg_color = Color(0.481302, 0.624292, 0.700334, 1)
corner_radius_top_left = 32
corner_radius_top_right = 32
corner_radius_bottom_right = 32
corner_radius_bottom_left = 32

[node name="GameOver" type="Control"]
z_index = 10
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_wpaxx")

[node name="Panel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_wpaxx")

[node name="Score" parent="." instance=ExtResource("3_jqelu")]
layout_mode = 1
anchors_preset = 2
anchor_left = 0.0
anchor_top = 1.0
anchor_right = 0.0
anchor_bottom = 1.0
offset_left = 275.0
offset_right = 275.0
grow_horizontal = 1
grow_vertical = 0
scale = Vector2(2, 2)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -116.5
offset_top = -41.5
offset_right = 116.5
offset_bottom = 41.5
grow_horizontal = 2
grow_vertical = 2
metadata/_edit_use_anchors_ = true

[node name="Label" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.408477, 0.649217, 0.741181, 1)
text = "GAME OVER!"
horizontal_alignment = 1

[node name="Retry" type="Button" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_colors/font_hover_color = Color(0, 0, 0, 1)
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_colors/font_focus_color = Color(0, 0, 0, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_styles/focus = SubResource("StyleBoxEmpty_jqelu")
theme_override_styles/hover = SubResource("StyleBoxFlat_w88jv")
theme_override_styles/pressed = SubResource("StyleBoxFlat_ckxb7")
theme_override_styles/normal = SubResource("StyleBoxFlat_73uea")
text = "Retry"

[node name="Quit" type="Button" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_colors/font_hover_color = Color(0, 0, 0, 1)
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_colors/font_focus_color = Color(0, 0, 0, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_styles/focus = SubResource("StyleBoxEmpty_jqelu")
theme_override_styles/hover = SubResource("StyleBoxFlat_w88jv")
theme_override_styles/pressed = SubResource("StyleBoxFlat_ckxb7")
theme_override_styles/normal = SubResource("StyleBoxFlat_73uea")
text = "Quit"
