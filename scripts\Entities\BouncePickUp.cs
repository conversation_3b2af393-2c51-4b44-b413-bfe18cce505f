using Godot;
using System;

public partial class BouncePickUp : Area2D
{
    [Export]
    private float MAX_BOUNCE_INCREMENT = 0.025f;

	public override void _Ready()
	{
		Connect("body_entered", new Callable(this, nameof(OnBodyEntered)));
	}

	private void OnBodyEntered(Node body)
	{
		if (body is Player player)
		{
			// Method 1: Use the player parameter directly (most efficient)
			GD.Print("(B)MaxBounceSpeed: " + player.bounceFallOff);
			player.bounceFallOff += MAX_BOUNCE_INCREMENT;
			GD.Print("(A)MaxBounceSpeed: " + player.bounceFallOff);

			PlayPickupSound();
			QueueFree();
		}
	}

	private void PlayPickupSound()
	{
		var pickupAudio = GetNode<AudioStreamPlayer2D>("AudioPickup");
		if (pickupAudio?.Stream != null)
		{
			// Create a persistent audio player that survives pickup destruction
			AudioStreamPlayer2D persistentAudio = new AudioStreamPlayer2D
			{
				Stream = pickupAudio.Stream,
				GlobalPosition = GlobalPosition,
				VolumeDb = pickupAudio.VolumeDb,
				PitchScale = pickupAudio.PitchScale
			};
			
			GetParent().AddChild(persistentAudio);
			persistentAudio.Play();
			persistentAudio.Finished += () => persistentAudio.QueueFree();
		}
	}
}
