[gd_scene load_steps=5 format=3 uid="uid://d2uemsath8aqu"]

[ext_resource type="Script" uid="uid://b44dfgi80j5uh" path="res://scripts/Entities/BouncePickUp.cs" id="1_tw00d"]
[ext_resource type="Texture2D" uid="uid://4vgx38ly1f1e" path="res://Assets/Entities/Planet4.png" id="2_cf2nx"]
[ext_resource type="AudioStream" uid="uid://di18khyj8whn7" path="res://Assets/Audio/Pickup.wav" id="3_cshs7"]

[sub_resource type="CircleShape2D" id="CircleShape2D_t3exx"]
radius = 56.0

[node name="BouncePickUp" type="Area2D"]
z_index = 1
collision_layer = 32
collision_mask = 32
monitorable = false
script = ExtResource("1_tw00d")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
scale = Vector2(0.25, 0.25)
shape = SubResource("CircleShape2D_t3exx")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(0.194944, 0.199957)
texture = ExtResource("2_cf2nx")

[node name="AudioPickup" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource("3_cshs7")
